/*
 * @Date: 2024-05-14 19:02:55
 * @Description: 添加/编辑教育经历
 */

import { refreshMyInfo } from '@/utils/helper/resume/index'
import { isEqualObj } from '@/utils/tools/common/index'
import { getInitFormData, getParams, handlerSubTime, isSubmit } from './utils'
import { store, actions } from '@/store/index'
import { dealDialogShow } from '@/utils/helper/dialog/index'
import { updateResume } from '../utils/index'

const defFormData: any = {
  /** 学校名称 */
  schoolName: '',
  /** 专业名称 */
  majorName: '',
  /** 专业类别ID */
  majorCategoryId: '',
  /** 专业类别名称 */
  majorCategoryName: '',
  /** 时间段 */
  timeData: '',
  /** 在校经历 */
  experience: '',
  /** 学历-学位 */
  eduBackground: '',
  /** 学历-是否全日制 */
  eduType: '',
  /** 开始时间 */
  startTime: '',
  /** 结束时间 */
  endTime: '至今',
  /** 学历回显文案 */
  eduStr: '',
}
Page(class extends wx.$.Page {
  waterMaskFiles = { images: [] }

  /** 首次提交的数据 */
  oldFormData = {}

  data = {
    title: '',
    /** 选择器是否显示, time, edu, majorCategory */
    visible: '',
    /** picker的value */
    pickerTimeValue: [],
    /** picker的value[] */
    pickerValueList: [],
    /** 时间类型, start: 开始时间， end: 结束时间 */
    type: 'start',
    /** 教育经历数据 */
    formData: {} as typeof defFormData,
    /** 简历uuid */
    resumeUuid: '',
    /** 是否显示专业 */
    isEduMajor: false,
    /** uuid */
    uuid: '',
    /** 专业类别数据 */
    majorData: [],
  }

  // 初始化
  async init() {
    const params = wx.$.nav.getData()
    const { resumeUuid } = store.getState().storage.myResumeDetails
    if (params && params.uuid) {
      // 编辑时需要传入整个对象
      const { formData, isEduMajor } = getInitFormData.call(this, params)
      this.setData({
        formData,
        isEduMajor,
        title: '编辑教育经历',
        uuid: params.uuid,
        resumeUuid,
        pickerTimeValue: [formData.startTime, formData.endTime],
        pickerValueList: [formData.eduBackground, formData.eduType],
      })
      this.oldFormData = wx.$.u.deepClone(formData)
    } else {
      const formData = getParams(defFormData)
      this.oldFormData = { ...formData }
      this.setData({ formData, title: '添加教育经历', uuid: '', resumeUuid })
    }

    // 获取专业类别数据
    await this.getMajorData()
  }

  onLoad() {
    this.init()
    updateResume()
  }

  /** 点击保存按钮 */
  async onSubmit() {
    await wx.$.u.waitAsync(this, this.onSubmit, [], 0, null, 'loading')
    const { formData, isEduMajor } = this.data
    const params = isSubmit(formData)

    if (!params) return

    // 验证专业类别
    if (isEduMajor && !formData.majorCategoryId) {
      wx.$.msg('请选择专业类别')
      return
    }

    if (isEqualObj(params, this.oldFormData)) {
      wx.$.r.back()
      return
    }
    wx.$.loading('保存中...')
    params.startTime = handlerSubTime(params.startTime)
    params.endTime = handlerSubTime(params.endTime)
    if (!isEduMajor) {
      params.majorName = ''
      params.majorCategoryId = ''
    }
    if (params.uuid) { // 编辑
      await this.editProject(params)
      return
    }
    // 添加
    await this.addProject(params)
    wx.hideLoading()
  }

  /** 添加教育经历 */
  addProject(params) {
    const paramsObj = { ...params, resumeUuid: this.data.resumeUuid }
    if (params?.eduType == 0) {
      paramsObj.eduType = null
    }
    return wx.$.javafetch['POST/resume/v3/edu/add'](paramsObj, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        this.submitSuccess()
      } else {
        wx.$.msg(res.message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  /** 编辑教育经历 */
  editProject(params) {
    const paramsObj = { ...params }
    if (params.eduType == 0) {
      paramsObj.eduType = null
    }
    return wx.$.javafetch['POST/resume/v3/edu/modify'](paramsObj, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        this.submitSuccess()
      } else {
        wx.$.msg(res.message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  /** 删除教育经历 loadingDel */
  async onDelete() {
    await wx.$.u.waitAsync(this, this.onDelete, [], 300, null, 'loadingDel')
    const { uuid } = this.data
    let dialogRes = await dealDialogShow({
      dialogIdentify: 'jioayujingli_querenshanchu',
    })
    const { itemClass, btnIndex } = dialogRes || { btnIndex: 0, itemClass: 'cancel' }
    if (itemClass == 'none') {
      dialogRes = await wx.$.confirm({
        content: '删除这条教育经历吗？',
        confirmText: '删除',
      }).then(() => {
        return { btnIndex: 1 }
      }).catch(() => {
        return { btnIndex: 0 }
      })
    }
    if (btnIndex == 0) {
      return
    }
    wx.$.loading('删除中...')
    wx.$.javafetch['POST/resume/v3/edu/delete']({
      uuid,
    }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        this.submitSuccess('删除成功')
      } else {
        wx.$.msg(res.message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  /** 提交成功的处理方法 */
  submitSuccess(msg = '保存成功') {
    refreshMyInfo()
    wx.$.msg(msg, 2000, true).then(() => {
      wx.$.r.back()
    })
  }

  /** 返回逻辑 */
  async onBack() {
    const { formData } = this.data
    if (isEqualObj(formData, this.oldFormData)) {
      wx.$.r.back()
      return
    }
    dealDialogShow({
      dialogIdentify: 'jlwbcfhts',
    }).then(res => {
      const { itemClass, btnIndex } = res || { btnIndex: 0, itemClass: 'cancel' }
      if (itemClass == 'none') {
        wx.$.confirm({
          content: '内容尚未保存,确定退出?',
        }).then(() => {
          wx.$.r.back()
        }).catch(() => {})
        return
      }
      if (btnIndex == 0) {
        return
      }
      wx.$.r.back()
    })
  }

  /** 点击时间选择器确定按钮 */
  onChangeTime({ detail }) {
    const { value } = detail
    this.setData({
      pickerTimeValue: value || [],
      'formData.timeData': `${value[0]}-${value[1]}`,
      'formData.startTime': detail.startTime || '',
      'formData.endTime': detail.endTime || '',
    })
    this.onHidePop()
  }

  /** 点击学历选择器确定按钮 */
  onChangeEdu({ detail }) {
    const { value, isEduMajor } = detail
    this.setData({
      pickerValueList: value || [],
      'formData.eduStr': detail.eduStr,
      'formData.eduBackground': detail.eduBackground,
      'formData.eduType': detail.eduType,
      isEduMajor,
    })
    this.onHidePop()
  }

  /** 设置内容-多行文本框 */
  onEdit(e) {
    const { formData } = this.data
    const { name, type } = e.currentTarget.dataset
    wx.$.nav.push(
      '/subpackage/resume/edit_textarea/index',
      { type },
      (data) => {
        formData[name] = data.content
        this.setData({ formData })
      },
      { content: formData[name] || '' },
    )
  }

  /** 设置内容-单行文本框 */
  onEditSingle(e) {
    const { formData } = this.data
    const { name, type } = e.currentTarget.dataset
    wx.$.nav.push(
      '/subpackage/resume/edit_input/index',
      { type },
      (data) => {
        formData[name] = data.content
        this.setData({ formData })
      },
      { content: formData[name] || '' },
    )
  }

  /** 显示弹框 */
  onShowPop(e) {
    const { type } = e.currentTarget.dataset

    if (type === 'majorCategory') {
      // 专业类别选择改为页面跳转
      wx.navigateTo({
        url: `/subpackage/resume/major-category/index?value=${this.data.formData.majorCategoryId || ''}&title=专业类别`,
      })
      return
    }

    this.setData({ visible: type })
  }

  /** 隐藏弹框 */
  onHidePop() {
    this.setData({ visible: false })
  }

  /** 获取专业类别数据 */
  async getMajorData() {
    try {
      const majorData = await store.dispatch(actions.resumeActions.fetchMajorData())
      this.setData({ majorData })
    } catch (error) {
      console.error('获取专业类别数据失败:', error)
    }
  }

  /** 处理专业类别选择 */
  onMajorCategoryChange(e) {
    const { detail } = e
    const { majorCategoryId, majorCategoryName } = detail
    this.setData({
      'formData.majorCategoryId': majorCategoryId,
      'formData.majorCategoryName': majorCategoryName,
    })
    this.onHidePop()
  }
})
