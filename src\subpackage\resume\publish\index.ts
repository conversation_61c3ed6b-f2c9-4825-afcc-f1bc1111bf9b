/*
 * @Date: 2021-12-31 11:22:02
 * @Description: 我的找活名片
 * query: {
 *  source: 'refreshAndTopEditAlert' // out->滚动到自我介绍模块(不需要了)
 * }
 */

import { actions, dispatch, store } from '@/store/index'
import { getResumeDetails, getGlobalDefaultConfig } from '@/utils/helper/resume/index'
import { resumeExistsHandler } from '../utils/index'
import {
  formatResumeDetails,
  fetchResumeRefresh,
  handlerShowDetail,
  resumePageBasis,
} from './utils/index'
import { getResumeSecond } from '@/utils/helper/resume/utils'
import { getShareInfoByTypeV2 } from '@/utils/helper/share/index'
import { SHARE_CHANNEL } from '@/config/share'

Page(class extends wx.$.Page {
  componentPage = ['onPageScroll']

  /** model数据 */
  useStore(state: StoreRootState) {
    const attachFileNumber = state.storage.myResumeDetails.attachFileNumber || 0
    const attachFileMax = wx.$.u.getObjVal(state.resume.globalConfig, 'resumeAttachUploadResp.attachFileMax')
    return {
      /** 当前的附件简历数量 */
      attachFileNumber,
      /** 附件简历最大数量 */
      attachFileMax,
      /** 是否对老板隐藏简历 0: 不隐藏 1: 隐藏 */
      hideStatus: state.resume.resumeExist.hideStatus,
    }
  }

  /** 是否执行onShow刷新逻辑 */
  triggerOnShow = false as string | boolean

  /** 其他页面进入的额外数据 */
  triggerData = {} as {showElem?: boolean}

  /** data数据 */
  data = {
    /** 是否显示附件简历的动画 */
    isAttachAnime: true,
    /** 是否显示悬浮的去完善卡片 */
    perfectSticky: false,
    /** 格式化之后的数据 */
    diyDetails: {
      /** 用户信息 */
      userInfo: {},
      /** 找活的uuid */
      resumeUuid: '',
      /** 找活基础信息 */
      basicInfo: {},
      /** 求职期望 */
      subJob: {
        jobFull: [],
        jobPart: [],
      },
    },
    /** 第二屏的数据 */
    diySecondDetails: {
      /** 工作经历 */
      workExpResp: [],
      /** 项目经历 */
      projectExpResp: [],
      /** 教育经历 */
      eduExpResp: [],
      /** 资格证书 */
      userCertificateInfos: [],
      /** 专业技能 */
      professionalSkills: {},
    },
    /** 是否显示指定的某些元素 */
    showElem: {
      isIntroduce: true, // 个人优势
      isCity: true, // 工作城市
      isWork: false, // 工作经历
      isProject: false, // 项目经历
      isEdu: false, // 教育经历
      isCertificate: false, // 资格证书
      isVideo: false, // 视频简历
      isAttachment: false, // 附件简历
      isProfessionalSkills: false, // 专业技能
    },
    /** 是否触发的次数 */
    showNum: 0,
    /** 弹框数据 */
    showPop: '',
    /** 是否初始化 */
    isInitLoad: false,
  }

  /** init数据 */
  async initLoad(options) {
    // 获取全局通用配置
    getGlobalDefaultConfig()
    wx.$.loading('加载中...')
    const details = store.getState().storage.myResumeDetails
    if (details && details.basicResp && details.basicResp.resumeUuid) {
      // 初始化处理
      formatResumeDetails(details).then((diyDetails) => {
        this.setData({ diyDetails })
      })
    }
    // 判断找活名片是否存在
    const exist = await resumeExistsHandler()
    if (!exist) {
      return
    }
    await this.onRefresh('refresh', true)
    this.setData({ isInitLoad: true })
  }

  async onLoad(options) {
    dispatch(actions.recruitIndexActions.setState({ classifyTabIdInit: true }))
    this.initLoad(options).catch(() => {
      wx.hideLoading()
    })
    // 我的在线简历回到首页需要更新信息流
    dispatch(actions.recruitIndexActions.setState({ _isUpdateInfoFlow: true }))
  }

  /** 页面显示时触发 */
  onShow() {
    const { triggerOnShow, triggerData } = this
    let showNum = this.data.showNum || 0
    showNum += 1
    if (triggerOnShow && showNum > 1) {
      this.onRefresh(triggerOnShow, triggerData.showElem)
      this.triggerOnShow = false
      this.triggerData = {}
    }
    this.setData({ showNum })
  }

  /** 埋点 */
  handlerPoint() {
    // const details = store.getState().storage.myResumeDetails

    // const id = wx.$.u.getObjVal(this.data.myResumeDetails, 'basicInfoResp.id')
    // this.introductionPoint = observerElementPoint.call(this, '#introduction', '个人介绍')
    // this.projectExperiencePoint = observerElementPoint.call(this, '#projectExperience', '工作经历')
    // this.interviewVideoPoint = observerElementPoint.call(this, '#interviewVideo', '面试视频')
    // this.resumeSkillsPoint = observerSkillsPoint.call(this, '#resumeSkills', id)
  }

  /** 组件刷新逻辑 */
  onRefreshChange(e) {
    const { type } = e.currentTarget.dataset
    this.onRefresh(type)
  }

  /** 更新页面简历数据
   * @param type 之外的则是刷新全部
   *  - refreshOne 只刷新第一屏的数据
   *  - refreshTwo 只刷新第二屏的数据刷新
   *  - load onLoad执行
   * @param isShowRule 是否处理显示规则
   */
  async onRefresh(type?, isShowRule = false) {
    wx.$.loading('加载中...')
    if (type == 'refreshOne') { // 只刷新第一屏的数据
      const resData = await getResumeDetails()
      const diyDetails = await formatResumeDetails(resData)
      this.setData({ diyDetails })
    } else if (type == 'refreshTwo') { // 只刷新第二屏的数据
      const resSecond = await getResumeSecond()
      this.setData({ diySecondDetails: resSecond })
    } else { // 刷新全部数据
      const [resData, resSecond] = await Promise.all([getResumeDetails(), getResumeSecond()])
      const diyDetails = await formatResumeDetails(resData)
      this.setData({
        diyDetails,
        diySecondDetails: resSecond,
      })
      if (type == 'load') {
        const iscertificateData = resSecond?.userCertificateInfos?.length > 0
        resumePageBasis({ is_certificate: iscertificateData ? 1 : 0 })
      }
    }
    wx.hideLoading()
    if (isShowRule) { // 处理显示规则
      handlerShowDetail().then((showElem) => {
        this.setData({ showElem })
      })
    }
  }

  onShareAppMessage(option) {
    return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, sharePage: 'DNOnlineResume', sharePath: 'DN_OnlineResume', ext: { }, from: option.from })
  }

  /** 下拉刷新 */
  onPullDownRefresh() {
    this.setData({ isAttachAnime: false })
    this.onRefresh().finally(() => {
      wx.hideLoading()
      wx.stopPullDownRefresh()
      wx.$.u.wait(1500).then(() => {
        this.setData({ isAttachAnime: true })
      })
    })
  }

  /** 找活法宝组件需要弹出支付的回调 */
  onPayPop(e) {
    if (e.detail && e.detail.popData) {
      this.setData({ showPop: e.detail.popData })
    }
  }

  /** 充值成功直接刷新名片 */
  async onRechargeSuccess() {
    fetchResumeRefresh({}).then(res => {
      // 充值后筛选
      this.onRefresh()
      if (res && res.popData) {
        this.setData({ showPop: res.popData })
      }
    })
  }

  /** 关闭弹框 */
  onClosePop() {
    this.setData({ showPop: '' })
  }

  /** 之后onShow执行刷新 */
  onShowRefresh() {
    this.triggerOnShow = 'refresh'
  }

  /** 供其他页面使用 */
  onOtherPage(type = 'refresh', triggerData = { }) {
    this.triggerData = triggerData
    if (`${type}`.indexOf('refresh') >= 0) {
      // 刷新
      this.triggerOnShow = type
    }
  }
})
