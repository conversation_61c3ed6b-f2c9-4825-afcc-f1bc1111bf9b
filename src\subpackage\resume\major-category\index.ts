/*
 * @Date: 2024-12-19
 * @Description: 专业类别选择页面
 */

import { store, actions } from '@/store/index'
import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'

const { top = 0, height = 0 } = getMenuButtonBoundingClientRect()

Page({
  data: {
    /** 专业类别数据 */
    majorData: [],
    /** 页面标题 */
    title: '专业类别',
    /** 顶部距离顶部的高度 */
    top: top + height,
    /** 左侧tab数据 */
    tabs: [],
    /** 当前选中的tab */
    activeKey: '',
    /** 右侧分组数据 */
    groups: [],
    /** 选中的值 */
    selected: {},
    /** 选中的项目 */
    selectedItem: null,
    /** 右侧滚动位置 */
    scrollTop: 0,
    /** 是否正在tab切换滚动 */
    isTabScrolling: false,
  },

  onLoad(options) {
    // 获取传入的已选择值
    const { value = '', title = '专业类别' } = options
    this.setData({ title })

    // 如果有传入的已选择值，设置到selectedItem中
    if (value) {
      this.setData({
        selectedItem: {
          value,
          label: '', // 这里先设为空，等数据加载后再设置正确的label
        },
      })
    }

    // 获取专业类别数据
    this.getMajorData()
  },

  /** 获取专业类别数据 */
  async getMajorData() {
    try {
      const majorData = await store.dispatch(actions.resumeActions.fetchMajorData())
      this.setData({ majorData })
      this.initData()
    } catch (error) {
      console.error('获取专业类别数据失败:', error)
      wx.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
    }
  },

  /** 初始化数据 */
  initData() {
    const { majorData } = this.data
    if (!majorData || majorData.length === 0) return

    // 构建tabs数据
    const tabs = majorData.map((category, index) => ({
      key: `category_${index}`,
      label: category.label,
    }))

    // 构建groups数据
    const groups = majorData.map((category, index) => ({
      key: `category_${index}`,
      label: category.label,
      options: category.children || [],
    }))

    // 设置默认左侧选中第一项
    this.setData({
      tabs,
      groups,
      activeKey: tabs[0]?.key || '',
    })

    // 如果有已选择的值，找到对应的左侧索引
    if (this.data.selectedItem?.value) {
      this.setSelectedValue(this.data.selectedItem.value)
    }
  },

  /** 设置已选择的值 */
  setSelectedValue(value) {
    const { majorData, groups } = this.data
    if (!value || !majorData || !Array.isArray(majorData) || !groups || groups.length === 0) return

    let activeKey = ''
    let selectedItem = null

    // 遍历查找已选择的值
    for (let i = 0; i < majorData.length; i++) {
      const category = majorData[i]
      if (category && Array.isArray(category.children)) {
        const found = category.children.find(child => child && child.value === value)
        if (found) {
          activeKey = `category_${i}`
          selectedItem = found
          break
        }
      }
    }

    if (activeKey) {
      this.setData({
        activeKey,
        selectedItem,
        selected: { [activeKey]: value },
      })

      // 延迟滚动到已选中的分组，禁用滚动动画
      setTimeout(() => {
        this.scrollToGroup(activeKey, false) // 第二个参数表示禁用动画
      }, 100)
    }
  },

  /** 左侧tab切换 */
  onTabChange(e) {
    const { keyValue } = e.detail
    this.setData({ activeKey: keyValue })

    // 设置标志位，防止滚动事件干扰
    this.setData({ isTabScrolling: true })

    // 滚动到对应的分组
    this.scrollToGroup(keyValue)

    // 延迟清除标志位
    setTimeout(() => {
      this.setData({ isTabScrolling: false })
    }, 500) // 给足够的时间让滚动完成
  },

  /** 滚动到指定分组 */
  scrollToGroup(groupKey, enableAnimation = true) {
    if (!groupKey) return

    // 通过组件实例调用滚动方法
    const selectableGroups = this.selectComponent('#selectable-groups')
    if (selectableGroups) {
      // 设置是否启用滚动动画
      selectableGroups.setData({ enableScrollAnimation: enableAnimation })
      selectableGroups.scrollToGroup(groupKey)
    }
  },

  /** 右侧分组选择变化 */
  onGroupsChange(e) {
    const { value, groupLabel, groupKey } = e.detail

    this.setData({
      selected: { [groupKey]: value },
      selectedItem: {
        value,
        label: groupLabel,
      },
    })

    // 返回上一页并传递选中的值
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    if (prevPage && prevPage.onMajorCategoryChange) {
      // 调用上一页的回调方法
      prevPage.onMajorCategoryChange({
        detail: {
          majorCategoryId: value,
          majorCategoryName: groupLabel, // 这里已经是具体的专业名称了
        },
      })
    }

    wx.navigateBack()
  },

  /** 右侧滚动事件 - 更新左侧tab */
  onScrollToGroup(e) {
    const { currentGroup } = e.detail
    this.setData({ activeKey: currentGroup })
  },

  /** 返回上一页 */
  onBack() {
    wx.navigateBack()
  },
})
