/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: 肖嘉俊 xia<PERSON><PERSON><PERSON><PERSON>@yupaowang.com
 * @Description: 获取自己的简历相关数据
 */

import { store, dispatch, actions, storage } from '@/store/index'
import { resumeDetailsDef, MyResumeDetails } from '@/store/model/storage/defData'
import { getResumeDetails } from '@/utils/helper/resume/index'

export type { templateList } from '@/subpackage/resume/utils/index.d'

type IResumeSecondScreen = YModels['POST/resume/v3/detail/app/myDetailSecondScreen']

/** 获取简历名片的工种id集合 */
function getOccIds(subs: MyResumeDetails['basicResp']['subs']): number[] {
  let occIds: number[] = []
  if (wx.$.u.isArrayVal(subs)) {
    occIds = subs.map((item) => item.occupationInfo.occId)
  }
  return occIds
}

/** 是否是白领用户 */
function boolWhiteCollar(subs: MyResumeDetails['basicResp']['subs']): boolean {
  let isWhiteCollar = false
  if (wx.$.u.isArrayVal(subs)) {
    for (let index = 0; index < subs.length; index += 1) {
      const { occupationInfo } = subs[index]
      if (occupationInfo && occupationInfo.occClassification == 1) {
        isWhiteCollar = true
        return true
      }
    }
  }
  return isWhiteCollar
}

/** 查询我的简历信息
 * @param resumeUuid 简历名片的UUID
 */
export function getResumeInfoMe(resumeUuid: string): Promise<MyResumeDetails> {
  return new Promise((resolve) => {
    wx.$.javafetch['POST/resume/v3/detail/app/myDetailFirstScreen']({ resumeUuid }, { hideMsg: true }).then(res => {
      if (res.error) {
        resolve({ ...resumeDetailsDef })
        return
      }
      const subs = wx.$.u.getObjVal(res, 'data.basicResp.subs') || []
      const resumeId = wx.$.u.getObjVal(res, 'data.basicResp.resumeId')
      const subCount = subs.length || 0
      const occIds = getOccIds(subs)
      const subFullCount = subs.filter((item) => item.positionType === 1).length
      const subPartCount = subCount - subFullCount
      const isWhiteCollar = boolWhiteCollar(subs)
      // eslint-disable-next-line no-param-reassign
      res.data.basicResp.subs = subs // 处理subs字段
      resolve({ ...res.data, isWhiteCollar, resumeUuid, resumeId, occIds, subCount, subFullCount, subPartCount })
    }).catch((err) => {
      console.error(err)
      let { myResumeDetails } = store.getState().storage
      myResumeDetails = wx.$.u.deepClone(myResumeDetails)
      resolve({ ...myResumeDetails })
    })
  })
}

/** 通用模板展示接口
 * @param occIds 职位ID 数组
 * @param isFilter 将控件信息过滤为status=1的
 */
export function getTempShow(occIds: number[], isFilter = true): Promise<YModels['POST/resume/v3/template/show']['Res']['data']> {
  return new Promise(async (resolve, reject) => {
    if (!wx.$.u.isArrayVal(occIds)) {
      reject({ error: true })
      return
    }
    const res = await wx.$.javafetch['POST/resume/v3/template/show']({
      occIds,
    }, { hideMsg: true })
    const templates = wx.$.u.getObjVal(res, 'data.templates') || []
    if (isFilter && wx.$.u.isArrayVal(templates)) {
      templates.forEach((item) => {
        const controlInfoList = wx.$.u.getObjVal(item, 'templateInfo.controlInfoList') || []
        if (wx.$.u.isArrayVal(controlInfoList)) {
          item.templateInfo.controlInfoList = controlInfoList.filter((control) => control.status == 1)
        }
      })
    }
    resolve(res.data)
  })
}

/** 查询我的简历信息(项目，教育经历，工作经历) */
export async function getResumeSecond(resumeUuid?: string): Promise<IResumeSecondScreen['Res']['data']> {
  let resumeUuidDiy = resumeUuid || wx.$.u.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')
  const errData = { projectExpResp: [], eduExpResp: [], workExpResp: [], userCertificateInfos: [] }
  if (!resumeUuidDiy) {
    resumeUuidDiy = (await dispatch(actions.resumeActions.fetchResumeExist())).resumeUuid
  }
  // dispatch, actions
  const handlerData = (data: IResumeSecondScreen['Res']['data']) => {
    // TODO: 专业技能接口对接lx
    const { projectExpResp, eduExpResp, workExpResp, userCertificateInfos, professionalSkills } = data || {}
    return {
      eduExpResp: eduExpResp || [],
      workExpResp: workExpResp || [],
      projectExpResp: projectExpResp || [],
      userCertificateInfos: userCertificateInfos || [],
      professionalSkills: professionalSkills || [],
    }
  }

  const res = await wx.$.javafetch['POST/resume/v3/detail/app/myDetailSecondScreen']({
    resumeUuid: resumeUuidDiy,
  }, { hideMsg: true }).catch((err) => err)

  if (!res || !res.data || res.error) {
    return errData
  }

  return handlerData(res.data)
}

/**
 * @description 新牛人完善的流程页面固定的参数-接口对应的变量名
 * @param {number} id 当前页面id（用于接口传参）
 * @param {string} modelGroupName 大模块组名
 * @param {string} modelName 当前模块（页面）名称
 * @param {string} pageUrl 当前模块（页面）原生路由
 * */
export const allNewWorkerPerfectList = [
  // 基础信息1
  {
    id: 1,
    modelGroupName: 'baseInfo',
    modelName: 'baseInfo',
    pageUrl: 'base_info',
    pageName: '基础信息',
  },
  // 最近一份工作2
  {
    id: 2,
    modelGroupName: 'workOcc',
    modelName: 'workOcc',
    pageUrl: 'recent_work',
    pageName: '最近一份工作',
  },
  // 求职期望（求职地点+职位)3
  {
    id: 3,
    modelGroupName: 'jobExpect',
    modelName: 'jobExpect',
    pageUrl: 'job_expect',
    pageName: '求职期望',
  },
  // 期望薪资4
  {
    id: 4,
    modelGroupName: 'hopeSalary',
    modelName: 'hopeSalary',
    pageUrl: 'salary_expect',
    pageName: '求职期望-薪资',
  },
  // 工作经历- 工作年限 5
  {
    id: 5,
    modelGroupName: 'workExp',
    modelName: 'firstWorkTime',
    pageUrl: 'first_work_time',
    pageName: '工作经历-工作年限',
  },
  // 工作经历-就职公司->6
  {
    id: 6,
    modelGroupName: 'workExp',
    modelName: 'company',
    pageUrl: 'assume_office',
    pageName: '工作经历-就职公司',
  },
  // 工作经历-工作时间->7
  {
    id: 7,
    modelGroupName: 'workExp',
    modelName: 'period',
    pageUrl: 'work_period',
    pageName: '工作经历-工作时间',
  },
  // 工作经历-工作内容->8
  {
    id: 8,
    modelGroupName: 'workExp',
    modelName: 'content',
    pageUrl: 'work_content',
    pageName: '工作经历-工作内容',
  },
  // 教育经历-最高学历->9
  {
    id: 9,
    modelGroupName: 'eduExp',
    modelName: 'degree',
    pageUrl: 'tallest_education',
    pageName: '教育经历-最高学历',
  },
  // 教育经历-学校名称->10
  {
    id: 10,
    modelGroupName: 'eduExp',
    modelName: 'school',
    pageUrl: 'school',
    pageName: '教育经历-学校',
  },
  // 教育经历-专业->11
  {
    id: 11,
    modelGroupName: 'eduExp',
    modelName: 'major',
    pageUrl: 'major',
    pageName: '教育经历-专业',
  },
  // 教育经历-就读时间->12
  {
    id: 12,
    modelGroupName: 'eduExp',
    modelName: 'period',
    pageUrl: 'study_time',
    pageName: '教育经历-就读时间',
  },
  // 个人优势->13
  {
    id: 13,
    modelGroupName: 'introduce',
    modelName: 'introduce',
    pageUrl: 'personal_advantage',
    pageName: '个人优势',
  },
  // 头像->14
  {
    id: 14,
    modelGroupName: 'headPortrait',
    modelName: 'headPortrait',
    pageUrl: 'add_head_photo',
    pageName: '添加头像',
  },
]

/** 获取完善简历流程对应的path */
export function getResumeProcessPath(modelName: string, type: string): string {
  const pageUrl = allNewWorkerPerfectList.find((item) => item.modelName === modelName && item.modelGroupName == type)?.pageUrl
  return pageUrl ? `/subpackage/resume/new_worker_perfect/${pageUrl}/index` : '/pages/index/index'
}

/** 获取当前流程配置以及提交的数据 */
export async function getResumeProcessData() {
  const resumeProcess = await dispatch(actions.resumeActions.getResumeConfig())
  const processList = resumeProcess.processList || []
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const currentProcess = allNewWorkerPerfectList.find((item) => `subpackage/resume/new_worker_perfect/${item.pageUrl}/index` == currentPage.route)
  if (processList.length && currentProcess) {
    const currentIndex = processList.findIndex((item) => item.type == currentProcess.modelGroupName && item.name == currentProcess.modelName)
    if (currentIndex > -1) {
      // 当前流程
      const process = processList[currentIndex]
      // 上一步流程
      let prev = currentIndex === 0 ? {} : processList[currentIndex - 1]
      /** 上一步取值逻辑：如果当前流程的type和上一步的type不同， 需要寻找到流程中与上一步相同type流程的第一个 */
      if ((currentIndex - 1) > 0 && prev.type != process.type) {
        prev = processList.find(item => item.type == prev.type)
      }

      // 下一步判断
      const next = getResumeProcessPath(currentIndex == processList.length - 1 ? '' : processList[currentIndex + 1]?.name, currentIndex == processList.length - 1 ? '' : processList[currentIndex + 1]?.type)
      // 跳过判断
      let jump = next
      if (process.type === 'workExp' || process.type === 'eduExp' || process.type === 'introduce') {
        const { name, type } = findJumpName(process.type, processList)
        jump = getResumeProcessPath(name, type)
      } else if (process.type === 'jobExpect') {
        /** 求职期望地点+职位-  跳过的时候，直接去职位列表页, 且： */
        jump = '/pages/index/index'
      }
      return {
        /** 当前流程配置 */
        processConfig: process,
        /** 当前流程数据 */
        processData: resumeProcess,
        /** 当前流程的值 */
        moduleValues: resumeProcess.moduleValues && resumeProcess.moduleValues[process.type],
        /** 下一步流程path */
        nextPath: next,
        /** 上一步流程path */
        prevPath: getResumeProcessPath(prev && prev.name, prev && prev.type),
        /** 跳过流程path */
        jumpPath: jump,
      }
    }
  }
  return {
    processConfig: { jumpSwitch: false, name: '' },
    processData: {},
    moduleValues: {},
    nextPath: '/pages/index/index',
    prevPath: '/pages/index/index',
    jumpPath: '/pages/index/index',
  }
}

/** 跳过路径-递归方法 */
function findJumpName(processType: string, processList: any[]): any {
  // 定义查找顺序
  const order = {
    workExp: ['eduExp', 'introduce', 'headPortrait'],
    eduExp: ['introduce', 'headPortrait'],
    introduce: ['headPortrait'],
  }

  // 获取当前类型的查找顺序
  const typesToCheck = order[processType] || []

  // 使用 some 方法遍历查找顺序
  const result = { name: '', type: '' }
  typesToCheck.some(type => {
    const index = processList.findIndex(item => item.type === type)
    if (index > -1) {
      result.name = processList[index].name
      result.type = processList[index].type
      return true // 找到匹配项，终止循环
    }
    return false
  })

  // 如果没有找到匹配项，返回空字符串
  return result
}

/**
 * @description 跳过当前流程
 * @param {string} curModule 当前流程
 * @param {string} path 跳转路径
*/
export const resumeProcessSkip = (curModule, name: string, path?: string) => {
  if (curModule) {
    wx.$.javafetch['POST/resume/v3/prepub/jumpModule']({ curModule }).then((res) => {
      if (res.code === 0 || res.code === 28022006) {
        // 跳过也需要请求 最终发布的接口
        autoSubmitProcess(name, false) && path && wx.$.r.replace({
          path,
        })
      }
    }).catch((err) => err)
  }
}

/** 是否是最后一个页面 & 提交最终的生成简历数据（后端自动生成） */
export const autoSubmitProcess = (name: string, isNeedWithOccupation = true) => {
  const { resumeProcess } = store.getState().resume
  const processList: any = resumeProcess?.processList || []

  if (processList?.length > 0 && processList[processList.length - 1].name == name) {
    wx.$.javafetch['POST/resume/v3/prepub/submit']().catch((err) => err)
    if (isNeedWithOccupation) {
      /** 完成整个流程后，需要在“找工作”页面，根据用户所填期望职位，为用户自动添加职位Tab。
      /* 1、职位Tab顺序：按照求职期望的选择顺序，正序排列。（注：如果无选择记录，则默认选择全部工作）
      /* 2、职位Tab下的城市选择：将期望城市中选择的城市，带入每个职位Tab下的城市筛选中。如果选择了多个，则只带入第一个。（注：如果无选择记录，则默认选择全国）
      */
      const { processValue } = store.getState().resume
      const { occupations, hopeArea } = processValue?.jobExpect || {}
      if (occupations && hopeArea) {
        handleCityData(hopeArea[0])
      }
    }
    dispatch(actions.storageActions.setCommonItem({ isResumeProcess: false }))
    const timer = setTimeout(() => {
      getResumeDetails()
      clearTimeout(timer)
    }, 1000)
    if(resumeProcess.jobOccIds) {
      wx.$.r.back()
    } else {
      wx.$.r.reLaunch({
        path: '/pages/index/index',
      })
    }
    return false
  }

  return true
}

/** 同步流程城市数据到列表 */
export const handleCityData = async (areaId) => {
  const city = await wx.$.l.getCityById(Number(areaId))
  const { userLocationCity } = store.getState().storage
  dispatch(actions.storageActions.setItem({ key: 'userLocationCity', value: { ...userLocationCity, recruitCityObj: { ...(city || {}), citys: [city], cityLen: 1 } } }))
}

/** 判断最后一页的 下一步的展示文案 */
export const getLastNextBtnText = (name) => {
  let text = '下一步'
  const { resumeProcess } = store.getState().resume
  const { processList: rpProcessList } = resumeProcess || {} as any
  const processList: any = rpProcessList || []
  if (processList?.length > 0 && processList[processList.length - 1].name == name) {
    text = '开启求职之路'
  }
  return text
}
