/*
 * @Date: 2022-03-22 09:42:08
 * @Description: 找活列表搜索结果页
 */

import { MapStateToData, connectPage, dispatch, actions, store, storage } from '@/store/index'
import { publishPage } from '@/lib/mini-component-page/index'
import { getMenuButtonBoundingClientRect, getDom } from '@/utils/tools/common/index'
import { resumeCardFormatDataV4 } from '@/utils/helper/resume/index'
import { cardViewHistory, getResumeRecommendList, isReportEvent, listExposure, reportFindWorkList } from '@/utils/helper/list/index'
import { setTDK } from '@/pages/resume/seo'
import resource from '@/components/behaviors/resource'
import { onScreePickerChange } from '@/pages/resume/components/position-tab/utils'

/** 列表请求接口的参数 */
export const requestParams = { /** 页码 */
  currentPage: 1,
  /** 工种id,用逗号分割 */
  occupationIdList: null,
  /** 搜索关键词 */
  keyword: '',
  /** 经纬度 */
  longitude: '',
  latitude: '',
  /** 标识是否工厂专区访问；[1,2,3]-主站访问;2-工厂专区访问;工厂专区后版本必传；
   * 主站为[1, 2, 3]
   */
  specialArea: [1, 2, 3],
  /** 每页记录数 */
  pageSize: 15,
}

const mapStateToData: MapStateToData = (state) => {
  const { storage, classify } = state
  const { resumeFilterSort, selectPositionTabId } = state.storage
  return {
    login: storage.userState.login,
    resumeOcc2Value: classify.sResumeOcc2Value,
    resumeFilterSort,
    selectPositionTabId,
  }
}

Page(
  connectPage(mapStateToData)(
    publishPage(['onReachBottom', 'onPullDownRefresh', 'onPageScroll'])({
      ...(!ENV_IS_SWAN ? { behaviors: [resource] } : {}),
      data: {
        keyword: '', // 搜索的关键词
        isNullStatus: 0, // 判断 无数据 情况下按钮文本显示是置顶或发布  用户招工状态 1:无正在招且审核通过的信息  2:有正在招且审核通过的信息，且有置顶权益   3：有正在招且审核通过的信息，且无置顶权益
        clientRect: getMenuButtonBoundingClientRect(), // 计算胶囊和标题高度
        firstLoaded: false, // 第一次加载列表是否已完成
        list: [],
        requestParams,
        resumeLoading: false,
        finish: false,
        isLogistics: false, // 是否是物流专区的
        logisticsClassfiedId: 0, // 物流专区返回的工种ID
        listScrollTop: 0,
        isSupply: false,
        isConnected: true, // 网络状态
        isNetWorkShow: false,
        enter_id: '5',
        /** 更换职位属性 */
        cpShow: false,
        cpList: [],
        cpVisible: false,
        cpJobId: '',
        cpCityIds: [],
        cpOccupationIds: [],
        cpDialogIdentify: '', // 选择职位弹框1，弹框2
        cpConfirmImInit: false, // 选择职位后，点击确定是否跳转IM会话详情
        /** 是否开启活跃标签AB实验 */
        isActiveAb: false,
        /** 是否开启列表城市工种未选中差异传参（https://yupaowang.feishu.cn/wiki/JQYowazt4iP2ejkzUuOciGWgngf） */
        isFilterAb: null,
      },
      async onLoad(options) {
        wx.$.u.isAbUi('resumeTags', 'newLogic').then(isActiveAb => {
          this.setData({ isActiveAb })
        })
        wx.$.u.isAbUi('cvFilterOptimizition', 'newLogic').then(isBool => {
          this.setData({
            isFilterAb: isBool,
          })
          const userLocationCity = storage.getItemSync('userLocationCity')
          const id = wx.$.u.getObjVal(userLocationCity, 'resumeCityObj.id')
          if (!id && !isBool) {
            dispatch(actions.storageActions.setItem({
              key: 'userLocationCity',
              value: {
                ...userLocationCity,
                resumeCityObj: { id: '1', name: '全国', letter: 'quanguo', level: 0, citys: [], cityLen: 0 },
              },
            }))
          }
        })
        if (ENV_IS_SWAN) {
          this.setData({ paddingTop: getMenuButtonBoundingClientRect().top })
        }
        await onScreePickerChange()
        // keywords url解码
        const keywords = decodeURIComponent(options.keywords)
        this.setData({ keyword: keywords, enter_id: options.enter_id || '5', requestParams: { ...requestParams, keyword: keywords } })
        this.init()
        this.eventTracking()
      },
      onUnload() {
        isReportEvent.call(this, this.data.list || [], (res) => this.uploadStatisticsData.call(this, res))
      },
      onHide() {
        // const p = getPageCode()
        isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
      },
      onShow() {
        // 处理列表置灰和以查看逻辑
        const list = cardViewHistory.getHistoryList('resume', this.data.list, 'resumeSubUuid')
        this.setData({ list })
        this.unfirstShow && this.data.list?.length && this.onListScroll(2)
        this.unfirstShow = true
        this.checkNeedChange()
      },
      /** 下拉刷新 */
      onPullDownRefresh() {
        // isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
        // new Promise((resolve) => {
        //   this.onRefresh(false)
        //   resolve('')
        // }).finally(() => {
        //   wx.stopPullDownRefresh()
        // })
        this.isRefresh = true
        // this.onIsChat({ detail: { have: false } })
      },
      // 初始化数据
      async init() {
        // 获取工种数据
        await this.getWorkClass()
        // 刷新列表
        this.onRefresh()
      },
      // 获取类别
      async getWorkClass() {
        // 获取工种列表数据
        try {
          await wx.$.l.getClassTreeData()
          const sResumeClassify = storage.getItemSync('sResumeClassify')
          const value = await wx.$.l.handleClassifyData(sResumeClassify)
          // 获取工种数据
          dispatch(actions.classActions.setSResumeClassify(value))
        } catch (error) {
          console.error(error)
        }
      },
      // 更新卡片拨打电话或聊一聊按钮状态
      async onCardBtnRefresh(resumeSubUuid, type) {
        const { list = [] } = this.data
        const idx = list.findIndex(item => item.resumeSubUuid == resumeSubUuid)
        if (idx < 0) {
          return
        }
        const item = list[idx] || {}
        const { rightInfo } = item || {}
        const { showImChatButton, showTelChatButton, hasImRight, hasTelRight } = rightInfo
        if (type == 'chat' && showImChatButton && !hasImRight) {
          this.setData({ [`list[${idx}]`]: { ...item, rightInfo: { ...rightInfo, hasImRight: true } } })
        } else if (type == 'call' && showTelChatButton && !hasTelRight) {
          this.setData({ [`list[${idx}]`]: { ...item, rightInfo: { ...rightInfo, hasTelRight: true } } })
        }
      },
      onNetWorkChange(e) {
        this.setData({ isConnected: e.detail.isConnected })
      },
      // 刷新
      onRefresh(showloading = true) {
        this.isRefresh = true
        setTimeout(() => {
          this.onService({ detail: { isFresh: true, showloading } })
          this.getJoinGroupParams()
          setTDK()
        }, 10)
      },
      /** 选择城市的回调事件-这个事件属于地址选择页里的回调 */
      onCityChange() {
        this.onRefresh()
      },
      onLocalChange() {
        this.onRefresh()
      },
      async getDiscountState(id) {
        const n = this.selectComponent(`.child-component-${id}`)
        const { data } = n || {}
        const { origin: d, btnObj, showContentBtn } = data || {}
        // 只有找活大列表和搜索列表才上报，其他不上报这个参数
        let offer_call = ((d === 'resumeIndex' || d === 'searchResult') ? '0' : null)

        if (n?.getDiscountState) {
          const s = await n.getDiscountState()
          offer_call = (s.discount && '1') || offer_call
        }

        let is_button_external = '0'
        const { isShow } = btnObj || {}
        if (showContentBtn && isShow) {
          is_button_external = '1'
        }
        return { offer_call, is_button_external }
      },
      // 列表组件用于获取数据
      async onService({ detail: { isFresh, resolve, showloading = true } }) {
        // 过滤选择器条件 store 里面取的
        const { requestParams, isLogistics, list, finish, firstLoaded } = this.data
        let { isFilterAb } = this.data
        // 获取工种数据及label，id
        const { sResumeClassify } = store.getState().storage
        const { resumeFilterScreen } = store.getState().listFilter
        const { filter, value } = resumeFilterScreen || {}
        const { age } = filter || {} as any
        let nResumeList = [...list]
        // 如果是第一页要 last_refresh_time_pos 设置为 0
        this.setData({ resumeLoading: true })
        // 获取工种数据及label，id
        const { sResumeOcc2Value } = store.getState().classify
        const nsResumeClassify = await wx.$.l.handleClassifyData(sResumeClassify)
        dispatch(actions.classActions.setSResumeClassify(nsResumeClassify))
        const params = {
          ...requestParams,
          pageSize: 15,
          ...(value || {}),
          ageRangeList: [],
        }
        if (age && age.length) {
          const [ageFrom, ageTo] = age
          params.ageRangeList = [{
            ageFrom,
            ageTo,
          }]
        }
        if (isFilterAb === null) {
          isFilterAb = await wx.$.u.isAbUi('cvFilterOptimizition', 'newLogic')
          this.setData({
            isFilterAb,
          })
        }
        if (wx.$.u.isArrayVal(sResumeOcc2Value)) {
          params.occupationIdList = sResumeOcc2Value.map(item => item.occIds).flat().filter(item => !!item)
        } else {
          params.occupationIdList = isFilterAb ? null : []
        }
        params.industryId = ''
        if (isFresh) {
          params.currentPage = 1
          nResumeList = []
          wx.pageScrollTo({ scrollTop: 0 })
        }
        const { isConnected, cpOccupationIds, cpCityIds } = this.data
        if (params.currentPage == 1) {
          showloading && firstLoaded && wx.showLoading({ title: '加载中...' })
          if (!isConnected) {
            this.setData({ isNetWorkShow: true })
          }
        } else if (!isConnected) {
          wx.$.msg('您的网络好像不太给力，请稍后再试')
          return
        }
        getResumeRecommendList.call(this, { ...params, areaFlag: this.data.isSupply ? 2 : 1 }, isFilterAb, 'search')
          .then(async (resData) => {
            const { res: result, params: nParams } = resData
            const { provinceId, cityId, areaId, areaIds, occupationIdList: jobIds, industryId } = nParams
            this.setData({ isNetWorkShow: false })

            const paramsKeys = Object.keys(resumeFilterScreen.value || {})
            paramsKeys.forEach(ky => {
              delete nParams[ky]
            })
            const sData: any = { requestParams: { ...nParams, currentPage: nParams.currentPage + 1 }, resumeLoading: false }
            const nCpCityIds = (areaIds || [areaId || cityId || provinceId]).filter(Boolean).map(id => Number(id))
            const nCpOccupationIds = jobIds
            if (!wx.$.u.arraysEqual(cpCityIds, nCpCityIds)) {
              sData.cpCityIds = nCpCityIds
            }
            if (!wx.$.u.arraysEqual(cpOccupationIds, nCpOccupationIds)) {
              sData.cpOccupationIds = nCpOccupationIds
            }
            const { isSupply, list: rList } = result.data || {} as any
            if (nParams.currentPage == 1) {
              sData.isSupply = isSupply
              if (!rList || rList.length == 0) {
                // 列表数据为空时,获取空状态是否显示按钮，显示什么按钮
                if (this.data.login) {
                  const res = await wx.$.javafetch['POST/job/v2/search/words/memberJobInfoVDoing']()
                  if (res && res.code == 0 && res.data) {
                    sData.isNullStatus = res.data.userJobStatus || 0
                  }
                } else {
                  sData.isNullStatus = 0
                }
              }
            }
            if (!isLogistics) {
              sData.isLogistics = false
            }
            const { data } = result || {}
            const { totalPage } = data || {} as any
            if (data.list.length <= 0 || totalPage <= nParams.currentPage) {
              sData.finish = true
            } else if (finish) {
              sData.finish = false
            }
            if (this.isRefresh) {
              this.isRefresh = false
              this.onIsChat({ detail: { have: false } })
            }
            const list = resumeCardFormatDataV4(rList)
            const dataList = params.currentPage == 1 ? [] : this.data.list
            const len = dataList.length
            // 已查看历史
            const hisList = cardViewHistory.getHistoryList('resume', list, 'resumeSubUuid').filter(
              (item, i, arr) => item.uuid
                && arr.findIndex((record) => {
                  return record.id === item.id
                }) === i,
            ).map((item, index) => {
              item.pagination_location = `${index + 1}`
              item.location_id = `${index + 1 + len}`
              item.pagination = params.currentPage
              item.source_id = '2'
              item.search_result = this.data.keyword || ''
              return item
            })
            sData.list = [...nResumeList, ...hisList]
            this.setData(sData)
            // 找活搜索页面列表数据曝光
            this.onListScroll(this.data.requestParams.page)
            return sData
          })
          .finally(() => {
            if (params.currentPage == 1) {
              showloading && firstLoaded && wx.hideLoading()
            }
            !this.data.firstLoaded && setTimeout(() => this.setData({ firstLoaded: true }), 150)
          })

        resolve && resolve()
      },
      /** 去置顶、发布 */
      async onTopOrPublish() {
        // 去置顶
        if (this.data.isNullStatus == 3) {
          wx.$.r.push({
            path: '/subpackage/recruit/published/index',
            query: { reback: 'toptips' },
          })
          return
        }
        wx.$.r.push({ path: '/subpackage/recruit/fast_issue/index/index' })
      },
      /** 获取加群组件的请求参数 */
      async getJoinGroupParams() {
        // 获取工种数据及label，id
        const { selectPositionTabId } = this.data
        const { selectItem } = selectPositionTabId
        const { sResumeOcc2Value } = store.getState().classify
        const { userLocationCity } = store.getState().storage
        const { resumeSearchCityObj, resumeCityObj } = userLocationCity || {} as any
        const userOccList = []
        let province
        // 获取工种数据及label，id
        await sResumeOcc2Value.map(item => item.occIds).flat().filter(item => !!item).map(async (item) => {
          const occV2 = await wx.$.l.getClassifyByIds([item])
          userOccList.push({
            occName: occV2[0].name,
            occId: item,
          })
        })
        if (resumeSearchCityObj) {
          province = (await wx.$.l.getAreaById(resumeSearchCityObj.id))?.province
        } else if (selectItem?.jobId) {
          province = (await wx.$.l.getAreaById(selectItem?.cityId))?.province
        } else {
          province = (await wx.$.l.getAreaById(resumeCityObj?.id))?.province
        }
        const params = {
          provinceName: province ? province.name : '',
          userOccList,
        }
        setTimeout(() => {
          this.setData({ joinGroupParams: params })
        }, 100)
      },
      /** 点击返回 */
      onBack() {
        wx.$.r.back()
      },
      /** 清除 */
      onClear() {
        this.setData({ keyword: '', requestParams: { ...this.data.requestParams, keyword: '' } })

        this.onRefresh()
      },
      /** 点击搜索框 */
      onSearchClick() {
        wx.$.r.back()
      },
      /** 跳转到工厂 */
      onToFactory() {
        wx.$.r.push({ path: '/pages/resume/index' })
      },
      /** 跳转到物流 */
      onToLogistics() {
        const { logisticsClassfiedId } = this.data
        wx.$.r.push({
          path: '/pages/resume/index',
          params: {
            classify_id: logisticsClassfiedId,
          },
        })
      },
      /** 监听列表滚动上报埋点 */
      onListScroll(page) {
        const { listScrollTop } = this.data
        if (!listScrollTop) {
          getDom('#resume-result-header').then((res) => {
            this.setData({ listScrollTop: res?.height || 0 })
            listExposure.call(this, {
              page,
              elementId: '.resume-result-item',
              top: -(res?.height || 0),
              callback: (res) => this.uploadStatisticsData.call(this, res),
            })
          })
          return
        }
        listExposure.call(this, {
          page,
          elementId: '.resume-result-item',
          top: -listScrollTop,
          callback: (res) => this.uploadStatisticsData.call(this, res),
        })
      },
      /** 埋点上报 */
      async uploadStatisticsData(res) {
        const otherEvent = {
          source_id: '2',
          search_result: this.data.keyword || '',
        }
        const { offer_call, is_button_external } = await this.getDiscountState(res.item.id)
        res.offer_call = offer_call
        reportFindWorkList(res, { ...otherEvent, is_button_external })
      },
      /** 搜索结果页--曝光埋点
     * source_id = '1' 招工 /'2'找活
     */
      eventTracking() {
        const { enter_id, keyword } = this.data
        const reportParams = {
          keywords: keyword,
          enter_id,
          source_id: '2',
        }
        wx.$.collectEvent.event('searchResultPageExposure', reportParams)
      },
      onDisableMove() { },
      async onDeleteItem(index) {
        const { list } = this.data
        list.splice(index, 1)
        const sData = { list, isNullStatus: 0 }
        if (!list || list.length == 0) {
          // 列表数据为空时,获取空状态是否显示按钮，显示什么按钮
          sData.isNullStatus = 0
          if (this.data.login) {
            const res = await wx.$.javafetch['POST/job/v2/search/words/memberJobInfoVDoing']()
            if (res && res.code == 0 && res.data) {
              sData.isNullStatus = res.data.userJobStatus || 0
            }
          }
        }
        this.setData({ sData })
      },
      // 判断onShow的时候是否需要重新请求更换职位接口
      checkNeedChange() {
        const { updatePositionShow, isSendCommunicate } = store.getState().index
        const { list, cpShow } = this.data
        if ((updatePositionShow || isSendCommunicate) && list.length && !cpShow && this.isChatReq) {
          this.setData({ cpShow: true })
        } else if (!updatePositionShow && list.length && cpShow && this.isChatReq) {
          this.setData({ cpShow: false })
        }
      },
      async onCpConfirm(e) {
        const changePosition = await wx.$.selectComponent.call(this, '#changePosition')
        changePosition.onConfirm(e)
        const { cpConfirmImInit, reGetImChat } = this.data
        if (cpConfirmImInit && reGetImChat) {
          const { jobId } = e.detail
          reGetImChat({ relatedInfoId: jobId })
          this.setData({ cpConfirmImInit: false, reGetImChat: null })
        }
      },
      onCpNoSend() {
        wx.$.javafetch['POST/reach/v2/im/chat/clearUserRelatedJobId']({}, { isNoToken: true, hideMsg: true }).then(async () => {
          const changePosition = await wx.$.selectComponent.call(this, '#changePosition')
          changePosition.resumeInit()
        })
        const { cpConfirmImInit, reGetImChat } = this.data
        if (cpConfirmImInit && reGetImChat) {
          reGetImChat()
          this.setData({ cpConfirmImInit: false, reGetImChat: null })
        }
      },
      onCpSelected(e) {
        const { jobId } = e.detail
        this.setData({ cpJobId: jobId })
      },
      onCpListChange(e) {
        const { list } = e.detail
        this.setData({
          cpList: list,
        })
      },
      onCpShowChange(e) {
        const { show } = e.detail
        if (!show) {
          this.isChatReq = false
        }
        this.setData({
          cpShow: show,
        })
      },
      onCpClick() {
        this.setData({
          cpVisible: true,
        })
      },
      onCpClose() {
        this.setData({
          cpVisible: false,
          cpConfirmImInit: false,
          reGetImChat: null,
        })
      },
      onItChange(e) {
        const { index } = e.currentTarget.dataset
        const { item } = e.detail
        this.setData({ [`list[${index}]`]: item })
      },
      onIsChat(e) {
        if (this.isRefresh) return
        const { have } = e.detail
        if (!have) {
          this.isChatReq = false
          this.setData({ cpShow: false })
        } else {
          const { cpShow } = this.data
          if (!this.isChatReq && !cpShow) {
            this.isChatReq = true
            this.setData({ cpShow: true })
          }
        }
      },
      async onSelectPostionBack(e) {
        const { dialogIdentify, reGetImChat } = e.detail
        this.setData({ cpDialogIdentify: dialogIdentify, cpConfirmImInit: true, reGetImChat })
        const changePosition = await wx.$.selectComponent.call(this, '#changePosition')
        await changePosition.resumeBossJobList()
        wx.hideLoading()
      },
    }),
  ),
)
