/*
 * @Date: 2022-03-10 14:22:31
 * @Description: 列表通用方法
 */
import { store, storage, dispatch, actions } from '@/store/index'
import { helper } from '@/utils/index'
import dayjs from '@/lib/dayjs/index'
import { isIos } from '@/utils/tools/validator/index'
import { rpxToPx } from '@/utils/tools/common/index'
import cardViewHistory from './cardViewHistory'
import { getBaseConfig } from '../common/index'

export { cardViewHistory }

/**
 * @name 格式化时间
 * @param {string} time 格式化的日期
 * @param {string} formatYear 格式化年
 * @param {string} formatMonth 格式化月
 * @param {string} formatDay 格式化日
 */
export function getFormattedDateStr(timeStr?: string, formatYear = '-', formatMonth: string = formatYear, formatDay: string = formatYear): string {
  let time: string = JSON.parse(JSON.stringify(timeStr))
  if (!time || typeof time !== 'string') {
    return ''
  }
  if (time.indexOf('年') !== -1 || time.indexOf('月') !== -1 || time.indexOf('日') !== -1) {
    return time
  }
  if (isIos() && time.indexOf('-') !== -1) {
    time = time.replace(/-/g, '/')
  }

  if (!Date.parse(time)) {
    return time
  }
  // const { getFullYear, getMonth, getDate, getHours, getMinutes, getSeconds } = new Date(time)
  const THIS_DATE = new Date(time)
  // eslint-disable-next-line max-len
  const [year, month, day, hours, minutes] = [
    THIS_DATE.getFullYear(),
    THIS_DATE.getMonth() + 1,
    THIS_DATE.getDate(),
    THIS_DATE.getHours(),
    THIS_DATE.getMinutes(),
    THIS_DATE.getSeconds(),
  ]
  const yearStr = year ? year + formatYear : ''
  const monthStr = month ? month + formatMonth : ''
  const dayStr = day ? day + formatDay : ''
  const hoursStr = hours < 10 ? `0${hours}:` : `${hours}:` || ''
  const minutesStr = hours && minutes < 10 ? `0${minutes}` : minutes || ''
  return `${formatYear && yearStr}${formatMonth && monthStr}${formatDay && dayStr} ${hoursStr}${minutesStr}`
}

/** 招工卡片数据清洗
 * @param isShowTop 是否展示置顶
 * @param isContactRecord 是否展示联系老板
 */
export const dealRecruitCard4 = (val, { selectLocation, isNearby } = {} as any) => {
  let item = val || {}
  const { login } = store.getState().storage.userState

  const { location } = item
  if (location && location.longitude && location.latitude) {
    /** 处理显示距离 */
    let locTxt
    if (isNearby) {
      // 如果是附近 距离为选择距离到信息的距离
      locTxt = helper.location.getLongitudeAndLatitudeText(location.longitude, location.latitude, selectLocation.longitude, selectLocation.latitude)
    } else {
      // 距离为信息距离到用户定位距离
      locTxt = helper.location.getProjectAddressDistanceTxt(`${location.longitude},${location.latitude}`, '', true)
    }

    item = { ...item, ...{ localtionOri: `${location.longitude},${location.latitude}`, location: locTxt } }
  }
  if (item.showDate || item.sortTime) {
    /** 处理日期 */
    const time = getFormattedDateStr(item.showDate || item.sortTime, '', '月', '日')
    item = { ...item, time }
  }
  /** 需要做匹配的路径 */
  const excludePath = ['subpackage/member/myContactHistory/index']
  /** 不显示水印、联系老板按钮界面-判断是否来源于会员中心我的联系记录、收藏 */
  const isDisplaySource = !excludePath.includes(wx.$.r.getCurrentPage().route)
  /** 已查看电话号码水印 显示联系老板按钮不显示水印 */
  const isDial = login && isDisplaySource && (item.dial || item.isLook)
  /** 是否显示联系老板按钮 */
  const showCallBoss = !login && isDisplaySource
  return {
    id: item.jobId,
    ...item,
    ...{
      isDial,
      showCallBoss,
    },
  }
}

/**
 * @name 是否为图片地址
 * @param imgUrl 图片地址
 * @returns boolean
 */
function isImgUrl(imgUrl: string): boolean {
  if (typeof imgUrl !== 'string') {
    return false
  }
  const reg = /^https?:\/\/(.+\/)+.+(\.(gif|png|jpg|jpeg|webp|svg|psd|bmp|tif))$/i
  return reg.test(imgUrl)
}

/**
 * @params: url: string 当前用户头像的地址
 * @params: type: boolean 是否是会员中心，如果是则返回会员中心的默认头像
 * @return: url: string 目前使用的头像地址
 * @description: 在百度小程序中，将默认头像替换为随机头像
 */
export const replaceAvatar = (url: string, type?: boolean): string => {
  if (ENV_IS_SWAN) {
    const urls = [
      '',
      'http://staticscdn.zgzpsjz.com/pc/images/head.png',
      'http://staticscdn.zgzpsjz.com/newyupao/images/user.png',
      'https://cdn.yupaowang.com/yupao_mini/yp_mini_zp_head_photo.png',
      'http://yupao.oss-cn-beijing.aliyuncs.com/newyupao/images/user.png',
      'https://cdn.yupaowang.com/yupao_mini/yp_mini_zp_head_photo.png',
    ]
    const defaultAvatarNum = 41
    if (urls.includes(url) || !isImgUrl(url)) {
      if (type) {
        return 'https://staticscdn.zgzpsjz.com/baidu_mini/img/baidu-head.png'
      }
      const num = Math.floor(Math.random() * (defaultAvatarNum - 1 + 1) + 1)
      return `http://staticscdn.zgzpsjz.com/baidu_mini/avatar/${num}.png`
    }
  }
  return url
}

/**
 * @function 处理存储的列表重复问题
 * @description 不改变传入的值,返回处理后的新值
 * @param listIds Array<number>
 * @param dataList IRecruitList
 * @param maxIsLen 默认500,最大存储listIds的长度，超过则覆盖开头的数组
 * @returns {Object} {listIds, dataList}
 */
export function handlerRecruitRepeat(listIds: Array<number>, dataList, maxIdsLen = 500) {
  let newListIds = listIds.concat() // 克隆数组（虽然是浅拷贝，但是数组里的值都是字面值，所以不会相互影响）
  const newDataList = dataList.filter((item) => {
    const id = Number(item.jobId) // 转成number类型
    // 判断是否是置顶并且处于listIds中
    if (item.top != 1 && newListIds.lastIndexOf(id) >= 0) {
      return false
    }
    newListIds.push(id)
    return true
  })

  // 长度超过500删除开头部分id
  if (newListIds.length > maxIdsLen) {
    const delLen = newListIds.length - maxIdsLen
    newListIds = newListIds.slice(delLen)
  }
  return {
    listIds: newListIds,
    dataList: newDataList,
  }
}

/** 处理过滤招满列表 */
export function filterFullList(reqParams, data, list, refreshCb) {
  /** 获取列表数据属性及已招满属性 */
  const fullProps = wx.$.u.deepClone(this.fullProps)
  if (!fullProps) {
    return list
  }
  // 判断当前页 状态为已招满的数据数量
  const fNum = (data.list || []).filter((item) => item.isEnd && item.isEnd.code == 2).length
  // 总的已招满数量
  const allFullNum = fullProps.fullInfo.fullNum + fNum
  // 如果已记录的已招满数据数量(fullNum)小于5 且当前页累加已招满数据数量(allFullNum)又大于5，就需要记录当前页的page
  // 那么下一页就需要过滤已招满数据
  if (fullProps.fullInfo.fullNum < 5 && allFullNum >= 5) {
    fullProps.fullInfo.fullPage = reqParams.currentPage
  }
  fullProps.fullInfo.fullNum = allFullNum
  // 如果已招满记录页(fullPage)存在，且当前页面的page大于记录页面的page且已招满记录数大于5，那么就需要过滤已招满数据
  const isFilter = data?.filter_job_is_end && data?.filter_job_is_end.is_switch

  const fullPageGreater5 = fullProps.fullInfo.fullPage
    && reqParams.currentPage > fullProps.fullInfo?.fullPage
    && fullProps.fullInfo.fullNum >= 5

  if (isFilter && fullPageGreater5) {
    // 如果当前请求页面的数据是15条，已招满数据也是15条，那么自动请求下一页数据
    if (data.list && data.list.length > 0 && fNum === data.list.length) {
      fullProps.fullInfo.continuityNum += 1
      // 过滤后为空数据，连续加载次数，不能超过后台设置的次数
      const counts = data?.filter_job_is_end?.page_count > 5 ? 5 : data.filter_job_is_end?.page_count
      if (fullProps.fullInfo.continuityNum <= counts) {
        refreshCb && refreshCb()
      }
      // 设置新的招满的属性
      this.fullProps = fullProps
    } else {
      // 没有提示过弹窗就提示，提示过了就不提示了
      if (!fullProps.fullInfo.isShow && data.list.length) {
        fullProps.fullInfo.isShow = true
        fullProps.isShowFullTips = true
        // 倒计时3秒后关闭弹窗
        const timerId = setTimeout(() => {
          this.fullProps = { ...fullProps, isShowFullTips: false }
          clearTimeout(timerId)
        }, 3000)
      }
      // 过滤已招满数据
      return list.filter((item) => item.isEnd && item.isEnd.code !== 2)
    }
  }
  return list
}

/**
 * 更新组件样式，查看图标、置灰效果
 * @param {array} list 数组列表
 * @param {boolean} updateRecord 是否更新积分
 * @param {boolean} gray 是否置灰
 */
export const updateRecruitListStyle = async (list, updateRecord = true, gray = true) => {
  if (!list?.length) {
    return []
  }
  const { login } = storage.getItemSync('userState')
  let expendIntegral = 0

  if (login && expendIntegral < 3 && updateRecord) {
    const { data } = await wx.$.javafetch['POST/integral/v1/memberIntegral/getMemberIntegral']({})
    expendIntegral = Number(data.expendIntegral)
  }

  let backList = list
  if (gray) {
    backList = cardViewHistory.getHistoryList('recruit', list)
  }
  return backList.map((item) => dealRecruitCard4(item))
}

/**
 * @description: 监听列表曝光
 * @param {number} page 列表分页
 * @param {string} elementId 列表元素id || class
 * @param {number} top 列表可视区域到页面顶部的距离
 * @param {Function} callback 埋点上报回调
 */
export async function listExposure(option: { page: number; elementId: string; top: number; bottom?: number; callback?: Function; callshow?: Function, isOnceCallShow?: boolean }) {
  if (ENV_IS_SWAN) {
    return
  }

  const { page, elementId, top, callback, callshow, bottom = 0, isOnceCallShow = false } = option
  const { appearNum, appearTime } = await getExposureConfig()
  if (this.observer) {
    this.observer.disconnect()
  }
  this.observer = this.createIntersectionObserver({ thresholds: [0, Number(appearNum) / 100], observeAll: true })
  this.observer.relativeToViewport({ top, bottom: bottom || 0 })
  /** 分页为1的时候重置数据 */
  if (!this.collectEventItems || page == 1) {
    /** 当前视口展示数据 */
    this.observerItems = new Map()
    /** 已上报数据 */
    this.collectEventItems = new Map()
    /** 曝光已上报数据 */
    this.callshowCollectEventItems = new Map()
  }
  this.observer.observe(elementId, async (res) => {
    const { dataset: { index, item }, intersectionRatio } = res
    if (this.collectEventItems.get(`${item?.id}`)) {
      return
    }
    if (intersectionRatio === 0 && this.observerItems?.has(`${index}`)) {
      const itemTime = this.observerItems.get(`${index}`)
      // 如果记录时间小于配置时间，不上报（场景：当前数据为页面切换记录的暂停曝光时间）
      if ((itemTime / 1000) < Number(appearTime)) {
        return
      }
      /** 曝光时长 */
      const exposureTime = (res.time - itemTime) / 1000
      if (exposureTime > Number(appearTime)) {
        this.collectEventItems.set(`${item?.id}`, 1)
        const { list = [] } = this.data
        const sliceList = list.slice(0, index + 1)
        const insertCardLen = sliceList.filter(item => item.isInsert).length || 0
        callback && callback({ item, exposureTime: `${exposureTime?.toFixed(2)}`, location_id: index + 1 - insertCardLen })
      }
      this.observerItems.delete(`${index}`)
      return
    }
    if (intersectionRatio > Number(appearNum) / 100) {
      if (!isOnceCallShow || !this.callshowCollectEventItems.get(`${item?.id}`)) {
        callshow && callshow({ item, location_id: index + 1 })
        this.callshowCollectEventItems.set(`${item?.id}`, 1)
      }

      let { time } = res
      if (this.observerItems?.has(`${index}`) && (this.observerItems.get(`${index}`) / 1000) < Number(appearTime)) {
        time -= this.observerItems.get(`${index}`)
      }
      this.observerItems.set(`${index}`, time)
    }
  })
}

/**
 * @description: 当前视窗内容是否需要上报埋点
 * @param {Array} list 列表数据
 * @param {Function} callback 埋点上报回调
 * @param {ext} 扩展参数
 */
export async function isReportEvent(list, callback) {
  if (ENV_IS_SWAN || !Array.isArray(list) || list.length === 0) {
    return
  }
  /** 当前时间戳 */
  const nowTime = new Date().getTime()
  const { appearTime } = await getExposureConfig()
  /** 页面离开前的曝光时长 */
  const pauseTime = new Map()
  this.observerItems?.forEach(async (value, index) => {
    this.observerItems
    /** 曝光时长 */
    const exposureTime = (nowTime - value) / 1000
    const doesNotNeedToExposed = exposureTime < Number(appearTime) || this.collectEventItems?.get(`${list[index]?.id}`)

    if (doesNotNeedToExposed) {
      !this.collectEventItems?.get(`${list[index]?.id}`) && pauseTime.set(`${index}`, nowTime - value)
      return
    }
    if (callback) {
      this.collectEventItems?.set(`${list[index]?.id}`, 1)
      // const n = this.selectComponent(`.child-component-${list[index].id}`)
      // const d = n?.data?.origin
      // // 只有找活大列表和搜索列表才上报，其他不上报这个参数
      // let offer_call = ((d === 'resumeIndex' || d === 'searchResult') ? '0' : null)

      // if (n?.getDiscountState) {
      //   const s = await n.getDiscountState(ext?.pageCode)
      //   offer_call = (s.discount && '1') || offer_call
      // }
      callback({ item: list[index], exposureTime: `${exposureTime?.toFixed(2)}`, location_id: Number(index) + 1 })
    }
  })
  this.observerItems = pauseTime
}

/** 获取列表埋点信息单次外露XX%,外露时间达到x秒 */
export const getExposureConfig = async () => {
  try {
    return (await getBaseConfig({ key: 'homePageBuriedPointConfig' })) || { appearNum: 80, appearTime: 2 }
  } catch (e) {
    return { appearNum: 80, appearTime: 2 }
  }
}

/** 招工列表埋点上报 */
export const reportRecruitList4 = async (res, eventData = {}) => {
  const { item, exposureTime, location_id } = res
  if (!item) {
    return
  }
  const { location, isEnd, buriedData, buriedPointData, isLook } = item || {}
  const showTags = (item?.showTags || []).map(item => Number((item && item.type) || 0))

  // 用功模式
  const occModeText = item.occMode === 1 ? '订单' : '招聘'
  // 置顶
  const topping = showTags.includes(8) ? '1' : '0'
  // 兼职
  const is_part_time = showTags.includes(13) ? '0' : '1'
  const freeText = isLook || (item.priceInfo && item.priceInfo.freeCount > 0) ? '免费' : '付费'

  // 信息状态  1-置顶信息、2-已招满、3---人工外呼核实中、4---联系老板
  let information_status = 4
  if (item.contactStatus == 2) {
    information_status = 3
  } else if ((isEnd && isEnd.code == 2) || isEnd == 2) {
    information_status = 2
  } else if (item.isTopShowBtn) {
    information_status = 1
  }

  /** 排序时间 */
  const sort_time = item.sortTime ? getSortTime(item.sortTime) : ''
  const statistics = {
    position_source: item?.tenant == 'YPHT' ? 2 : 1,
    info_user_id: String(item.userId || ''),
    pagination: String(item.pagination || ''),
    pagination_location: String(item.pagination_location || ''),
    referrer_page: '',
    landing_page: '',
    search_result: '',
    real_name_view: '',
    source: '',
    source_id: '',
    type_options: '',
    job_options: '',
    active_label: '',
    is_part_time: item?.showTags ? is_part_time : '-99999',
    province_id: '',
    city_id: '',
    county_id: '',
    urgent: '-99999',
    topping: item?.showTags ? topping : '-99999',
    check_degree: item.checkDegreeStatus || item.checkDegreeStatus == 0 ? `${item.checkDegreeStatus}` : '',
    exposure_duration: exposureTime,
    location_id: `${location_id || ''}`,
    info_id: String(item.jobId || item.id),
    detailed_address: item.address || '',
    free_information: isLook || item.priceInfo ? freeText : '-99999',
    post_distance: location || '',
    position_status: isEnd ? `${isEnd.code || isEnd}` : '-99999',
    sort_time,
    information_status,
    occupations_type: item.occMode !== undefined ? occModeText : '',
    job_location: item.localtionOri || '',
    keywords_source: item.keywords_source || '',
    is_famous_company: !item.companyInfo || item.companyInfo.famous === undefined ? '' : `${item.companyInfo.famous}`,
    recommend_reason: item.recommendedReason || '',
    ...(buriedData || {}),
    ...(buriedPointData || {}),
    ...eventData,
  }
  statistics.info_card_type = item.isInsert ? '2' : '1'
  wx.$.collectEvent.event('homeWorklistExposure', statistics)
}

export const SOURCE_ID_NAME_DATA = {
  1: '首页简历简历列表',
  2: '首页搜索简历列表',
  3: '找活收藏',
  4: '详情底部推荐简历列表',
  5: '谁看过我底部推荐简历列表',
  12: 'push',
  13: '站内信',
  14: '我的收藏简历列表',
  15: 'IM卡片简历详情',
  16: '物流列表',
  17: '工厂列表',
  18: '工厂搜索列表',
  19: '谁联系过我底部推荐简历列表',
  20: 'IM头像简历详情',
  21: '我看过谁简历列表',
  22: '我联系的人简历列表',
  23: '谁联系过我简历列表',
  24: '左右滑动简历详情',
  25: '谁看过我简历列表',
  26: '小程序分享',
}

/** 找活列表埋点上报 */
export const reportFindWorkList = async (res, eventData = {}) => {
  const { item, exposureTime, location_id, offer_call } = res
  if (!item) {
    return
  }
  const { buriedPointData, location, search_result, id, uuid, guid, pagination, pagination_location,
    userInfo, activeStatusText, occupation, occupationStr } = item || {}
  const { userId } = userInfo || {}
  let post_distance: any = await wx.$.l.getProjectAddressDistance(location)
  if (post_distance) {
    post_distance = (post_distance / 1000).toFixed(2)
    if (post_distance == '0.00') {
      post_distance = ''
    }
  }
  let occupations_type = '-99999'
  const { mode } = occupation || {}
  if (mode == 2) {
    occupations_type = '招聘'
  } else if (mode == 1) {
    occupations_type = '订单'
  }

  const statistics: any = {
    search_result: search_result || '',
    info_id: `${id || ''}`,
    info_user_id: `${userId || ''}`,
    resume_uuid: `${uuid}`,
    request_id: `${guid}`,
    location_id: `${location_id || ''}`,
    pagination: `${pagination}`,
    pagination_location: `${pagination_location}`,
    exposure_duration: `${exposureTime}`,
    post_distance: `${post_distance || ''}`,
    active_status: activeStatusText || '',
    occupations_v2: `${(occupation || {}).occId || ''}`,
    occupations_v2_name: occupationStr || '',
    referrer_page: '',
    landing_page: '',
    occupations_type: `${occupations_type}`,
    offer_call: `${offer_call || ''}`,
    ...eventData,
    ...(buriedPointData || {}),
  }

  statistics.offer_call = statistics.offer_call == 0 ? '0' : (statistics.offer_call || '-99999')
  statistics.source_id = statistics.source_id || '-99999'
  statistics.source = SOURCE_ID_NAME_DATA[statistics.source_id] || ''
  statistics.is_button_external = statistics.is_button_external == 0 ? '0' : (statistics.is_button_external || '-99999')
  delete statistics.model_score
  delete statistics.uid
  delete statistics.recall_source
  wx.$.collectEvent.event('liveListDisplay', statistics)
}

/** 排序时间 */
export const getSortTime = (time) => {
  const sort_time = time || Date.now()
  return dayjs(sort_time).format('MM月DD日 HH:mm')
}

/** 检测是否需要进行滚动操作 */
export const checkScrollTop = (currentScrollTop, bannerHeight, cb?) => {
  /** 需要滚动的距离实际就是金刚区的高度加上margin-bottom，因为顶部的搜索区域不在文档流中，不算高度  */
  const banner = bannerHeight + (bannerHeight > 0 ? rpxToPx(16) : 0)
  const scrollTop = banner
  const currentScrollTop_int = Math.floor(currentScrollTop) + 1 // 浮点转整数 // +1 处理空列表情况
  const scrollTop_int = Math.floor(scrollTop) // 浮点转整数
  if (currentScrollTop_int >= scrollTop_int) {
    // 整数比较
    cb && cb(scrollTop)
    return
  }
  wx.pageScrollTo({
    // 滚动到的位置（距离顶部 px）
    scrollTop,
    duration: 100, // 滚动所需时间 如果不需要滚动过渡动画，设为0（ms）
    complete: () => {
      setTimeout(() => {
        cb && cb({ scrollTop, isScroll: true })
      }, 101) // 安卓的 complete 回调立即执行，不是在 duration 后执行
    },
  })
}

/**
 * 获取找活名片列表信息
 * @param params 请求参数
 * @param type 请求专区
 * @param isFilterAb 是否开启列表城市工种未选中差异传参（https://yupaowang.feishu.cn/wiki/JQYowazt4iP2ejkzUuOciGWgngf）
 *
 *  */
// eslint-disable-next-line sonarjs/cognitive-complexity
export async function getResumeRecommendList(params, isFilterAb, type: 'resume' | 'search' | 'factory' | 'logistics' = 'resume') {
  const nParams: any = { ...params }
  const { selectPositionTabId } = this.data
  const { selectItem } = selectPositionTabId || {}
  const { jobId: hasJobId } = selectItem || {}
  const area = store.getState().storage.userLocationCity
  // 判断是否显示底部推荐文件 true不显示 注：若用户本身就是选的 省+工种，无其他筛选，则不展示分割线下补数数据且去掉图示中提示文字“符合要求的牛人已展示…”
  const isNoTjDataTips = false
  // 组件城市参数
  if (type == 'factory' || type == 'logistics') {
    if (area && area.id != '1') {
      if (area.level == 1 || (area.id == area.pid && area.level == 2)) {
        nParams.areaId = area.id
      } else if (area.level == 2) {
        nParams.areaId = area.pid
        if (!nParams.isSupply) {
          nParams.areaId = area.id
        }
      } else if (area.level == 3 && area.parents && area.parents[0]) {
        nParams.areaId = area.parents[0].id
        if (!nParams.isSupply) {
          nParams.areaId = area.pid
        }
      }
    } else {
      nParams.areaId = ''
    }
  } else {
    const { userLocationCity } = store.getState().storage
    const { resumeCityObj, resumeSearchCityObj, id } = userLocationCity || {} as any
    const city = (type == 'search' ? (resumeSearchCityObj || resumeCityObj) : resumeCityObj) || {}
    const { citys, id: nId } = city || {}
    if (wx.$.u.isArrayVal(citys)) {
      nParams.areaIds = citys.map(item => item.id).filter(Boolean)
    } else if (nId == null) {
      nParams.areaIds = isFilterAb ? null : ['1']
    } else if (nId || id) {
      nParams.areaIds = [nId || id].filter(Boolean)
      // if (nParams.isSupply) {
      //   nParams.areaIds = [nPid || pid].map(item => (item == 'hmt' || item == '1' || !item || item == '0' ? id : item))
      //   if ((nLevel || level) == 2 && wx.$.u.isArrayVal(citys) && citys.length > 1) {
      //     nParams.areaIds = [id]
      //   }
      // }
    }
    if (hasJobId && (type !== 'search' || (!resumeSearchCityObj && type === 'search'))) { // 选择职位tab栏
      const { selectItem } = selectPositionTabId
      if (type !== 'search') {
        nParams.occupationIdList = selectItem.selectOccList
      }
      nParams.areaIds = selectItem.selectAreas
      this.selectJobid = wx.$.u.deepClone(selectPositionTabId.selectItem?.jobId)
    }
  }
  // 组装用户经纬度
  const { userLocation } = store.getState().storage
  if (userLocation) {
    const ll: Array<String> = userLocation.split(',')
    if (ll && ll.length == 2) {
      // eslint-disable-next-line prefer-destructuring
      nParams.longitude = ll[0]
      // eslint-disable-next-line prefer-destructuring
      nParams.latitude = ll[1]
    }
  }
  nParams.isSupply && delete nParams.isSupply
  const L_IS_GEO_AUTH = storage.getItemSync('L_IS_GEO_AUTH')
  const userLocation_city = storage.getItemSync('userLocation_city')
  if (L_IS_GEO_AUTH == 1 && userLocation_city) {
    const { adcode } = userLocation_city as any
    const area = await wx.$.l.getAreaByAdcode(adcode)
    const { current } = area || {}
    const { id } = current || {}
    if (id) {
      nParams.gpsAreaId = id
    }
  }
  if (type !== 'search' && !hasJobId) {
    nParams.occupationIdList = []
  }
  const res = await wx.$.javafetch['POST/resume/v3/list/app/list'](nParams)
  // 处理职位tab切换数据展示不对
  if (type !== 'search' && selectPositionTabId.selectItem?.jobId && this.selectJobid !== selectPositionTabId.selectItem?.jobId) {
    return {}
  }
  return { res, params: nParams, isNoTjDataTips }
}

/** 保存历史搜索记录 */
export async function saveSearchHistory(key: 'recruit' | 'resume', keywords: string) {
  const value = wx.$.u.deepClone(store.getState().storage.searchHistory)
  value[key].unshift(keywords)
  const index = value[key].lastIndexOf(keywords)
  if (index > 0) {
    // 处理重复数据
    value[key].splice(index, 1)
  }
  if (value[key].length > 6) {
    // 数据大于过6个时,删除末尾的数据
    value[key].pop()
  }
  dispatch(actions.storageActions.setItem({ key: 'searchHistory', value }))
}
