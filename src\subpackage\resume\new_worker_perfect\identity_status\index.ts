/*
 * @Date: 2024-12-01 10:00:00
 * @Description: 身份选择及求职状态选择
 */

import { getLastNextBtnText, getResumeProcessData } from '@/utils/helper/resume/utils'

Page(class extends wx.$.Page {
  data = {
    identityInfo: {
      identity: 0, // 身份：1-职场人，2-学生
      jobStatus: 0, // 求职状态：1-随时到岗，2-月内到岗，3-考虑机会，4-暂不考虑
    },
    processConfig: {},
    // 下一步流程path
    nextPath: '',
    // 上一步流程path
    prevPath: '',
    /** 跳过目的页面 */
    jumpPath: '',
    bottomHeight: 0,
    /** 下一步的文案 */
    btnText: '下一步',
  }

  async onLoad() {
    const processInfo = await getResumeProcessData()
    const btnText = getLastNextBtnText(processInfo.processConfig.name)
    this.setData({ 
      ...processInfo, 
      identityInfo: processInfo.moduleValues || this.data.identityInfo, 
      btnText 
    })
  }

  /** 身份选择 */
  onIdentityChange(e: any) {
    const { dataset } = e.currentTarget || {}
    const { value } = dataset || {}
    const identityInfo = wx.$.u.deepClone(this.data.identityInfo)
    
    identityInfo.identity = parseInt(value)
    // 切换身份时重置求职状态
    identityInfo.jobStatus = 0
    
    this.setData({ identityInfo })
  }

  /** 求职状态选择 */
  onJobStatusChange(e: any) {
    const { dataset } = e.currentTarget || {}
    const { value } = dataset || {}
    const identityInfo = wx.$.u.deepClone(this.data.identityInfo)
    
    identityInfo.jobStatus = parseInt(value)
    
    this.setData({ identityInfo })
  }

  /** 提交 */
  onSubmit(e) {
    const { fn } = e.detail || {}
    const { identityInfo, nextPath } = this.data
    
    // 验证身份选择
    if (!identityInfo.identity) {
      wx.$.msg('请选择身份')
      return
    }
    
    // 验证求职状态选择
    if (!identityInfo.jobStatus) {
      wx.$.msg('请选择求职状态')
      return
    }

    // 根据身份确定下一步跳转路径
    let targetPath = nextPath
    if (identityInfo.identity === 1) {
      // 职场人身份：进入求职期望选择页面
      targetPath = '/subpackage/resume/new_worker_perfect/job_expect/index'
    } else if (identityInfo.identity === 2) {
      // 学生身份：进入教育经历填写-最高学历、类型选择页面
      targetPath = '/subpackage/resume/new_worker_perfect/tallest_education/index'
    }

    // 提交数据到后端
    wx.$.javafetch['POST/resume/v3/prepub/pubUserInfo']({
      itemId: 2, // 假设身份选择的itemId为2
      ...identityInfo,
    }).then((res) => {
      if (res.code === 0) {
        fn && fn(identityInfo)
        wx.$.nav.replace(targetPath)
      }
    }).catch(() => {})
  }
})
