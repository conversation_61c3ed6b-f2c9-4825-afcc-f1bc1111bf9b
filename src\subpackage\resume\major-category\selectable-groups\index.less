.selectable-groups {
  padding: 0 24rpx 0 24rpx;
  .safe-area(24rpx)
}

.group-label {
  padding: 24rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000D9;
  margin-right: 8rpx;
}

.options {
  display: flex;
  flex-wrap: wrap;
}

.option-item {
  position: relative;
  width: calc(50% - 13rpx);
  box-sizing: border-box;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #000000D9;
  text-align: center;
  background-color: #F5F7FCFF;
  border: 2rpx solid transparent;
  margin-bottom: 20rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.option-item:nth-child(2n+1){
  margin-right: 20rpx;
}

.option-item.selected {
  border-color: #0092FFFF;
  color: #0092FFFF;
  background-color: #e8f4ff;
  font-weight: bold;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
