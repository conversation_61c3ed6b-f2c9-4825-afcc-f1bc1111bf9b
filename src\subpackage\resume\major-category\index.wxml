<custom-header title="{{title}}" customBack border bind:back="onBack"/>

<view class="major-category-page" style="height: calc(100vh - {{ top }}px - 8rpx)">
  <view class="category-container">
    <!-- 左侧一级分类 -->
    <view class="left-category">
      <select-bar tabs="{{ tabs }}" activeKey="{{ activeKey }}" bind:change="onTabChange"></select-bar>
    </view>

    <!-- 右侧二级分类 -->
    <view class="right-category">
      <selectable-groups
        id="selectable-groups"
        initGroups="{{ groups }}"
        selected="{{ selected }}"
        isTabScrolling="{{ isTabScrolling }}"
        bind:change="onGroupsChange"
        bind:scrollToGroup="onScrollToGroup"
      />
    </view>
  </view>
</view>
