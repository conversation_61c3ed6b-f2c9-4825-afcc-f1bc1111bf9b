/* 自定义添加社团组织经历组件样式 */

/* 主容器样式 */
.box {
  padding-top: 40rpx;
}

/* 添加项容器样式 */
.add-box {
  margin-top: 40rpx;
}

/* 添加项样式 */
.add-box-item {
  padding: 48rpx 24rpx;
  background: #F5F7FCFF;
  border-radius: 16rpx;
}

/* 添加图标样式 */
.add-icon {
  color: #545458;
}

/* 响应式布局类 */
.dis-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.cnt-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.mb-24 {
  margin-bottom: 24rpx;
}

/* 字体样式类 */
.fz-34 {
  font-size: 34rpx;
}

.fz-32 {
  font-size: 32rpx;
}

.fz-40 {
  font-size: 40rpx;
}

.fz-bold {
  font-weight: bold;
}

/* 颜色样式类 */
.black-09 {
  color: rgba(0, 0, 0, 0.9);
}
