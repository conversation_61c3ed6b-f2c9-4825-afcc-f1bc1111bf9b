<custom-header title="{{title}}" customBack border bind:back="onBack"/>

<view class="body">
  <form-item
    title="学校"
    bind:click="onEditSingle"
    value="{{formData.schoolName}}"
    data-name="schoolName"
    data-type="eduSchoolName"
  />

  <form-item
    title="学历"
    bind:click="onShowPop"
    value="{{formData.eduStr}}"
    data-name="eduBackground"
    data-type="edu"
    placeholder="请选择"
  />

  <form-item
    wx:if="{{isEduMajor}}"
    title="专业类别"
    bind:click="onShowPop"
    value="{{formData.majorCategoryName}}"
    data-name="majorCategory"
    data-type="majorCategory"
    placeholder="请选择"
  />

  <block wx:if="{{isEduMajor}}">
    <form-item
      bind:click="onEditSingle"
      isEllip
      title="专业"
      value="{{formData.majorName}}"
      data-name="majorName"
      data-type="eduMajorName"
    />
  </block>

  <form-item
    bind:click="onShowPop"
    title="时间段"
    value="{{formData.timeData}}"
    data-name="jobContent"
    data-type="time"
  />

  <view class="desc">以下为选填项</view>

  <form-item
    bind:click="onEdit"
    isEllip
    title="在校经历"
    value="{{formData.experience}}"
    data-name="experience"
    data-type="eduExperience"
    placeholder="选填 请输入"
  />

</view>

<m-button-footer isCustom>
  <view class="footer">
    <button wx:if="{{!!uuid}}" bind:tap="onDelete" loading="{{loadingDel}}" class="f-btn btn-del">删除</button>
    <button bind:tap="onSubmit" loading="{{loading}}" class="f-btn btn-save">保存</button>
  </view>
</m-button-footer>

<!-- 学历选择器 -->
<edu-picker
  type="{{type}}"
  visible="{{visible == 'edu'}}"
  value="{{pickerValueList}}"
  bind:submit="onChangeEdu"
  bind:cancel="onHidePop"
/>

<!-- 时间选择器 -->
<school-time-picker
  type="{{type}}"
  visible="{{visible == 'time'}}"
  value="{{pickerTimeValue}}"
  bind:submit="onChangeTime"
  bind:cancel="onHidePop"
/>
