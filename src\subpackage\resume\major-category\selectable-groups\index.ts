import { getMenuButtonBoundingClientRect, throttle } from '@/utils/tools/common/index'

const { top = 0, height = 0 } = getMenuButtonBoundingClientRect()

Component({
  properties: {
    initGroups: {
      type: Array,
      value: [], // { key, label, options: [{ value, label }] }
    },
    selected: {
      type: Object,
      value: {}, // 选中的值
    },
    isTabScrolling: {
      type: Boolean,
      value: false, // 是否正在执行tab点击滚动
    },
    enableScrollAnimation: {
      type: Boolean,
      value: true, // 是否启用滚动动画
    },
  },

  data: {
    top: top + height, // 顶部距离顶部的高度
    currentGroup: '', // 当前滚动到的分组的key
    scrollIntoView: '', // 用于控制锚点跳转
    groups: [], // { key, label, options }
    scrollTop: 0, // 当前滚动位置
    scrollIntoViewIng: false, // 是否正在执行scrollIntoView
  },

  // 监听器
  observers: {
    initGroups(groups) {
      if (groups && groups.length > 0) {
        this.setData({ groups: wx.$.u.deepClone(groups) }) // 初始化分组数据
        this.syncSelectedToGroups(this.data.selected) // 同步初始化已选中的数据到分组
      }
    },
    selected(selected) {
      if (this.data.groups && this.data.groups.length > 0) {
        this.syncSelectedToGroups(selected) // 同步选中状态
      }
    },
    isTabScrolling(isTabScrolling) {
      // 只有当值真正改变时才更新，避免无限循环
      if (this.data.isTabScrolling !== isTabScrolling) {
        this.setData({ isTabScrolling })
      }
    },
  },

  methods: {
    // 获取分组位置信息
    async getGroupPositions() {
      return new Promise((resolve) => {
        const query = this.createSelectorQuery()
        const positions = []

        this.data.groups.forEach((group) => {
          query.select(`#${group.key}`).boundingClientRect()
        })

        query.exec((res) => {
          if (res && Array.isArray(res)) {
            res.forEach((rect, index) => {
              if (rect) {
                positions.push({
                  keyValue: this.data.groups[index].key,
                  top: rect.top,
                  bottom: rect.bottom,
                  height: rect.height,
                })
              }
            })
          }
          resolve(positions)
        })
      })
    },

    // 同步选中状态到groups
    syncSelectedToGroups(selected = {}) {
      if (!this.data.groups || !Array.isArray(this.data.groups)) return

      // 检查是否有实际变化，避免无限循环
      let hasChanges = false
      const updatedGroups = this.data.groups.map(group => {
        if (!group || !group.options || !Array.isArray(group.options)) return group

        const updatedOptions = group.options.map(option => {
          if (!option) return option
          // 检查这个选项是否被选中
          const isSelected = Object.values(selected).includes(option.value)
          if (option.selected !== isSelected) {
            hasChanges = true
          }
          return {
            ...option,
            selected: isSelected,
          }
        })

        return {
          ...group,
          options: updatedOptions,
        }
      })

      // 只有当有实际变化时才更新数据
      if (hasChanges) {
        this.setData({ groups: updatedGroups })
      }
    },

    // 选中选项
    onSelectItem(e) {
      const { group: groupKey, value, groupLabel } = e.currentTarget.dataset

      if (!groupKey || !value || !groupLabel) {
        console.warn('选中选项时缺少必要参数:', { groupKey, value, groupLabel })
        return
      }

      // 更新选项的选中状态
      const updatedGroups = this.data.groups.map(group => {
        if (group && group.key === groupKey && Array.isArray(group.options)) {
          return {
            ...group,
            options: group.options.map(option => {
              if (!option) return option
              return {
                ...option,
                selected: option.value === value,
              }
            }),
          }
        }
        return group
      })

      this.setData({ groups: updatedGroups })

      // 触发选中事件，传递具体的专业名称
      this.triggerEvent('change', {
        groupKey,
        value,
        groupLabel, // 现在是具体的专业名称，如"力学类"
        selected: { [groupKey]: value },
      })
    },

    // 滚动事件
    onScroll: throttle(async function () {
      // 如果正在执行tab点击滚动，则忽略滚动事件
      if (this.data.isTabScrolling) {
        return
      }

      const groupPositions = await this.getGroupPositions() // 获取每个分组的位置

      if (this.data.scrollIntoViewIng) {
        this.setData({ scrollIntoViewIng: false })
        return
      }

      // 遍历所有分组，检查当前滚动位置属于哪个分组
      for (let i = 0; i < groupPositions.length; i++) {
        const { keyValue, top } = groupPositions[i]
        if (top >= 52) {
          if (this.data.currentGroup !== keyValue) {
            // 只有当滚动到不同的分组时，才触发更新
            this.setData({ currentGroup: keyValue })
            this.triggerEvent('scrollToGroup', { currentGroup: keyValue }) // 通知父组件
          }
          break
        }
      }
    }, 5),

    // 滚动到指定分组
    scrollToGroup(groupKey) {
      if (!groupKey) return

      // 根据是否启用滚动动画来决定滚动方式
      if (this.data.enableScrollAnimation) {
        // 设置scrollIntoView来滚动到指定分组
        this.setData({
          scrollIntoView: groupKey,
          scrollIntoViewIng: true, // 标记正在执行scrollIntoView
        })

        // 延迟清除scrollIntoView，避免影响后续滚动
        setTimeout(() => {
          this.setData({
            scrollIntoView: '',
            scrollIntoViewIng: false,
          })
        }, 100)
      } else {
        // 不使用动画，直接计算滚动位置
        this.scrollToGroupWithoutAnimation(groupKey)
      }
    },

    // 不使用动画的滚动方法
    scrollToGroupWithoutAnimation(groupKey) {
      if (!groupKey) return

      // 计算目标分组的位置
      const query = this.createSelectorQuery()
      query.select(`#${groupKey}`).boundingClientRect()
      query.select('.selectable-groups').boundingClientRect()

      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const groupRect = res[0] // 目标分组的位置
          const scrollViewRect = res[1] // 滚动容器的位置

          if (groupRect && scrollViewRect) {
            // 计算需要滚动的距离
            const scrollTop = groupRect.top - scrollViewRect.top + this.data.scrollTop

            this.setData({
              scrollTop: Math.max(0, scrollTop),
            })
          }
        }
      })
    },
  },
})
