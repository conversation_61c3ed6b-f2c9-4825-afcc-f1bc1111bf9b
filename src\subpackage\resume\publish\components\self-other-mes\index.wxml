<!-- 自定义添加社团组织经历组件 -->
<view class="box" wx:if="{{shouldShowBox}}">
  <!-- 标题 -->
  <view class="fz-34 fz-bold">自定义添加</view>
  <!-- 添加项容器 -->
  <view class="dis-flex flex-column add-box">
    <!-- 专业技能添加项 - 当没有专业技能时显示 -->
    <view class="add-box-item dis-flex cnt-between mb-24 align-center" wx:if="{{!hasProfessionalSkills}}" bindtap="handAddSkill" data-type="major">
      <text class="fz-32 black-09 fz-bold">专业技能</text>
      <icon-font type="yp-add" size="40rpx" color="rgba(0,0,0, 0.65)" />
    </view>
    <!-- 社团/组织经历添加项 - 当没有组织经历时显示 -->
    <view class="add-box-item dis-flex cnt-between mb-24 align-center" wx:if="{{!hasOrganizationExperiences}}" bindtap="handAddSkill" data-type="group">
      <text class="fz-32 black-09 fz-bold">社团/组织经历</text>
      <icon-font type="yp-add" size="40rpx" color="rgba(0,0,0, 0.65)" />
    </view>
    <!-- 实践活动添加项 - 当没有实践活动时显示 -->
    <view class="add-box-item dis-flex cnt-between mb-24 align-center" wx:if="{{!hasPractices}}" bindtap="handAddSkill" data-type="activity">
      <text class="fz-32 black-09 fz-bold">实践活动</text>
      <icon-font type="yp-add" size="40rpx" color="rgba(0,0,0, 0.65)" />
    </view>
    <!-- 荣誉奖励添加项 - 当没有荣誉奖励时显示 -->
    <view class="add-box-item dis-flex cnt-between align-center" wx:if="{{!hasAwards}}" bindtap="handAddSkill" data-type="reward">
      <text class="fz-32 black-09 fz-bold">荣誉奖励</text>
      <icon-font type="yp-add" size="40rpx" color="rgba(0,0,0, 0.65)" />
    </view>
  </view>
</view>