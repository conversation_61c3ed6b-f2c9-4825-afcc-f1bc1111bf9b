.contain {
  padding: 40rpx 0;
  border-bottom: 1rpx solid #E9EDF3;
}

.work-box .work-item:nth-child(1) {
  padding-top: 0 !important;
}

.work-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #E9EDF3;
}

.work-box .work-item:nth-last-child(1) {
  border-bottom: none !important;
  padding-bottom: 0 !important;
}

.desc-box {
  display: flex;
  align-items: center;
}

.user-strength {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  line-height: 40rpx;
  word-break: break-all;
}

.placeholder {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 40rpx;
}
