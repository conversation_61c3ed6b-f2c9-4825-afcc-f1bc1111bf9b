.worker-perfect {
  min-height: 100vh;
  background: #fff;
}

.cell {
  padding: 0 32rpx 24rpx 32rpx;
}

.cell-title {
  font-size: 50rpx;
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: bold;
  line-height: 54rpx;
  padding: 32rpx 0;
}

/* 身份选择样式 */
.identity-options {
  width: 100%;
  display: flex;
  justify-content: space-between;
  color: rgba(105, 64, 64, 0.85);
  font-size: 30rpx;
}

.identity-item {
  width: 331rpx;
  height: 104rpx;
  line-height: 104rpx;
  border-radius: 16rpx;
  background: #f5f7fc;
  text-align: center;
  cursor: pointer;
}

.identity-item.selected {
  border: 2rpx solid #0092ff;
  font-weight: bold;
  color: #0092FF;
  box-sizing: border-box;
  background: #e0f3ff;
}

.identity-tip {
  margin: 24rpx 0;
  color: rgba(0, 0, 0, 0.45);
  font-size: 28rpx;
  line-height: 36rpx;
}

/* 求职状态选择样式 */
.job-status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.job-status-item {
  position: relative;
  width: 100%;
  height: 112rpx;
  border-radius: 16rpx;
  background: #f5f7fc;
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  box-sizing: border-box
}

.job-status-item.selected {
  height: 108rpx;
  border: 2rpx solid #0092ff;
  color: #0092FF;
  box-sizing: border-box;
  background: #e0f3ff;
}

.recommend-tag {
  position: absolute;
  top: -16rpx;
  left: 0;
  display: flex;
  align-items: center;
  background: #0092FF;
  color: #fff;
  font-size: 26rpx;
  padding: 8rpx 16rpx;
  border-radius:  16rpx 16rpx 16rpx 0px;
  gap: 4rpx;
  .img {
    width: 32rpx;
    height: 32rpx;
  }
}

.status-text {
  text-align: center;
  line-height: 1.4;
}


.job-status-tip {
  margin-top: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  line-height: 36rpx;
}
