.worker-perfect {
  min-height: 100vh;
  background: #fff;
}

.cell {
  padding: 32rpx;
}

.cell-title {
  font-size: 38rpx;
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: bold;
  line-height: 54rpx;
  padding-bottom: 24rpx;
}

/* 身份选择样式 */
.identity-options {
  width: 100%;
  display: flex;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
}

.identity-item {
  width: 331rpx;
  height: 104rpx;
  line-height: 104rpx;
  border-radius: 16rpx;
  background: #f5f7fc;
  text-align: center;
  cursor: pointer;
}

.identity-item.selected {
  border: 2rpx solid #0092ff;
  color: #0092FF;
  box-sizing: border-box;
  background: #e0f3ff;
}

.identity-tip {
  margin-top: 16rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  line-height: 36rpx;
}

/* 求职状态选择样式 */
.job-status-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.job-status-item {
  width: 100%;
  height: 104rpx;
  line-height: 104rpx;
  border-radius: 16rpx;
  background: #f5f7fc;
  text-align: center;
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.job-status-item.selected {
  border: 2rpx solid #0092ff;
  color: #0092FF;
  box-sizing: border-box;
  background: #e0f3ff;
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.job-status-tip {
  margin-top: 16rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  line-height: 36rpx;
}
