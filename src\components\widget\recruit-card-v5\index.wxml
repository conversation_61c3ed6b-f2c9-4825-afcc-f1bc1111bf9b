<view class="recruit-card {{isNeedMarginTop ? '' : 'no_margin_top'}} {{isNeedMarginBottom ? '' : 'no_margin_bottom'}}" style="{{customStyle}}" data-item="{{item}}" catch:tap="onJumpOperate">
  <view class="content">
    <!-- 已招满图标 is_check为2信息审核通过时 is_end为2已招满 -->
    <block wx:if="{{!isContactRecord && (item.isEnd.code == 2 || item.contactStatus == 2)}}">
      <view class="tag-icon">
        <image class="icon" lazy-load src="https://staticscdn.zgzpsjz.com/miniprogram/images/ly/yp-mini_recruit_status_full_icon.png" ></image>
      </view>
    </block>
    <!-- 招工标题 -->
    <view class="title-box">
      <view style="color: {{item.viewed ? 'rgba(0, 0, 0, 0.451)' : 'rgba(0, 0, 0, 0.851)'}}">
        <rich-text nodes="{{title}}"></rich-text>
      </view>

        <!-- 薪资信息 -->
        <view wx:if="{{isNewZPCard && item && item.showTags && item.showTags.length > 0}}" class="salary-new">
          <block wx:for="{{item.showTags}}" wx:for-item="itemName"  wx:key="index">
            <view class="salary-text is-new-zp-card" wx:if="{{itemName.type == 1}}">
              {{itemName.name}}
            </view>
          </block>
        </view>
    </view>
    <!-- 薪资信息 -->
    <view class="salary" wx:if="{{!isNewZPCard && item && item.showTags && item.showTags.length > 0}}">
      <block wx:for="{{item.showTags}}" wx:for-item="itemName"  wx:key="index">
        <view class="salary-text" wx:if="{{itemName.type == 1}}">
          {{itemName.name}}
        </view>
      </block>
    </view>
    <!-- 标签 -->
    <view class="tags" style="color: {{item.viewed ? 'rgba(0, 0, 0, 0.451)' : 'rgba(0, 0, 0, 0.651)'}}">
      <view class="cont">
        <view wx:if="{{item && item.showTags && item.showTags.length > 0}}" class="recruit-tags {{ item.isLook && !item.showCallBoss ? 'is-viewed': ''}}">
          <block wx:for="{{item.showTags}}" wx:for-item="tagItem" wx:key="index">
            <view wx:if="{{tagItem.type !== 1 && tagItem.type !== 8 && tagItem.type !== 9 && tagItem.type !== 10 && tagItem.type !== 11 && tagItem.type !== 12}}" class="r-tag" >{{tagItem.name}}</view>
          </block>
        </view>
      </view>
      <!-- 已查看图标 -->
      <view class="viewed-img" wx:if="{{item.isLook && !item.showCallBoss && isShowLookend}}">
        <image class="icon" lazy-load src="https://cdn.yupaowang.com/yp_mini/images/jl/yp-mini_recruit_viewed.png" />
      </view>
    </view>
    <!-- 地址信息 -->
    <view class="info">
      <view class="address-box">
        <view  wx:if="{{item.address}}" class="address-text">{{item.address}}</view>
        <view wx:if="{{item.location && !item.showCallBoss && isLocation}}" class="address-count">{{item.location}}</view>
      </view>
      <view wx:if="{{item.occMode !== 2}}" class="time">{{item.updateDate}}</view>
    </view>
    <!-- 用户信息 -->
    <view class="user-footer">
      <view class="user-box">
        <view class="user-avatar-box">
          <image class="user-avatar" wx:if="{{!imgErr}}" src="{{item.headerImage}}" mode=""  binderror="handleImgErr" ></image>
          <view class="user-avatar fallback" wx:else/>
          <!-- 学生身份不展示在线状态 -->
          <view wx:if="{{item.isActive && !isStudent}}" class="user-online"></view>
        </view>
        <view class="user-info-box">
          <view wx:if="{{item.userName ||( item.companyInfo && item.companyInfo.brandName)}}" class="user-name" style="color: {{item.viewed ? 'rgba(0, 0, 0, 0.451)' : 'rgba(0, 0, 0, 0.851)'}}" >{{item.userName}}<text wx:if="{{item.userName && item.companyInfo && item.companyInfo.brandName}}">・</text><text wx:if="{{item.companyInfo && item.companyInfo.brandName}}">{{item.companyInfo.brandName}}</text></view>
          <view wx:elif="{{isStudent}}" class="user-name" style="color: {{item.viewed ? 'rgba(0, 0, 0, 0.451)' : 'rgba(0, 0, 0, 0.851)'}}">先生</view>
          <!-- 学生身份不展示活跃度与回复信息 -->
          <block wx:if="{{!isStudent}}">
            <view wx:if="{{item.replyInfo && (item.replyInfo.time || item.replyInfo.count)}}"  class="user-desc">{{item.replyInfo.time}}<text wx:if="{{item.replyInfo.time && item.replyInfo.count}}">·</text>{{item.replyInfo.count}}</view>
            <view wx:else class="user-desc">{{item.activeDate}}</view>
          </block>
        </view>
      </view>
      <view wx:if="{{!isNewZPCard}}" class="user-call">
        <block wx:if="{{item.userId != userId || !isLogin}}">
          <block wx:if="{{item.contactStatus == 2 || !isShowOutBtn}}"></block>
          <!-- 联系按钮(电话) -->
          <view wx:elif="{{btnObj.isShow && btnObj.type == 'phone'}}" class="boss-btn" catch:tap="onCallPhoneBtn">
              <icon-font custom-class="icon" type="yp-phone_new" size="24rpx" color="#0092ff"></icon-font>
              <view class="text">{{btnObj.btnText}}</view>
          </view>
          <!-- 联系按钮(im) -->
          <view wx:elif="{{btnObj.isShow && btnObj.type == 'chat'}}" class="boss-btn" catch:tap="onGoToChat">
              <icon-font custom-class="icon" type="yp-chat" size="24rpx" color="#0092ff"></icon-font>
              <view class="text">{{btnObj.btnText}}</view>
          </view>
        </block>
      </view>
    </view>
    <!-- 推荐理由 -->
    <view wx:if="{{item.recommendedReason}}" class="recommend-box">
      <image class="icon" lazy-load src="https://cdn.yupaowang.com/yupao_mini/星光.svg" ></image>
      <text >{{item.recommendedReason}}</text>
    </view>
</view>