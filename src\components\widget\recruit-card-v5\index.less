.recruit-card {
  position: relative;
  background-color: #fff;
  margin: 16rpx 24rpx;
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  color: #333;
  font-size: 30rpx;
  overflow: hidden
}

.no_margin_top {
  margin-top: 0rpx !important;
}

.no_margin_bottom {
  margin-bottom: 0rpx !important;
}

.content {
  padding: 32rpx 24rpx 16rpx 24rpx;
  box-sizing: border-box;

  .tag-icon {
    position: absolute;
    top: 0;
    left: 0;

    .icon {
      width: 80rpx;
      height: 72rpx;
    }
  }

  .title {
    display: flex;
    align-items: center;
    word-break: break-all;
    font-size: 34rpx;
    font-weight: bold;
    line-height: 48rpx;
  }
  // 卡片名企样式
  .famous-icon {
    height: 40rpx;
    vertical-align: middle;
    margin-right: 8rpx;
    transform: translateY(-4rpx);
  }

  // 急聘样式
  .urgent-tag, .campus-recruit-tag, .intern-tag   {
    display: inline-flex;
    margin-left: 8rpx;
    height: 36rpx;
    min-width: 56rpx;
    padding: 0 8rpx;
    border-radius: 8rpx;
    border: 1rpx solid #e8362e;
    background: #ffebec;
    color: #e8362e;
    font-weight: 500;
    font-size: 20rpx;
    line-height: 20rpx;
    box-sizing: border-box;
    transform: translateY(-6rpx);
    align-items: center;
  }

  .campus-recruit-tag {
    background: rgba(224, 243, 255, 1);
    color: rgba(0, 146, 255, 1);
    border: 1rpx solid rgba(0, 146, 255, 1);
  }
  .intern-tag {
    background: rgba(224, 243, 255, 1);
    color: rgba(0, 146, 255, 1);
    border: 1rpx solid rgba(0, 146, 255, 1);
  }

  // 富文本兜底样式 - 专门的类名避免冲突
  .rich-text-fallback {
    .famous-icon {
      display: inline-block;
      vertical-align: middle;
      flex-shrink: 0; // 防止图标被压缩
    }

    .urgent-tag, .campus-recruit-tag, .intern-tag {
      display: inline-block;
      vertical-align: middle;
      flex-shrink: 0; // 防止标签被压缩
      white-space: nowrap; // 防止标签内部换行
    }
  }

  .tags {
    display: flex;
    flex-direction: row;
    padding: 16rpx 0;
    position: relative;

    &.ac {
      align-items: center;
    }

    .cont {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .viewed-img {
      position: absolute;
      right: 0;
      bottom: -4rpx;

      .icon {
        width: 71rpx;
        height: 63rpx;
      }
    }
  }

  .footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    box-sizing: border-box;
    width: 100%;
    line-height: 30rpx;
    color: #808080;
    vertical-align: middle;
  }

  .boss-btn {
    display: flex;
    align-items: center;
    background: #e5f4ff;
    border-radius: 12rpx;
    color: #0092ff;
    font-size: 24rpx;
    line-height: 1;
    flex-shrink: 0;
    padding: 16rpx 14rpx;

    .icon {
      margin-right: 8rpx;
    }

    .text {
      font-weight: bold;
    }
  }
}

.address {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  line-height: 40rpx;
  height: 40rpx;
  color: rgba(0, 0, 0, 0.45);
  overflow: hidden;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.address-tex {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.45);
}

.recruit-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  height: 48rpx;
  overflow: hidden;

  &.is-viewed {
    margin-right: 104rpx;
  }

  .r-tag {
    flex-shrink: 0;
    padding: 0 12rpx;
    font-size: 26rpx;
    margin-right: 8rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    border-radius: 8rpx;
    background: #f5f6faff;

    &:last-child {
      margin-right: 0;
    }
  }

  .hightLightTag {
    background: rgba(224, 242, 255, 1);
    color: rgba(0, 146, 255, 1);
  }
}

.img {
  width: 102rpx;
  height: 40rpx;
  float: left;
  display: inline;
  position: relative;
  top: 4rpx;
  left: 0;
  margin-right: 8rpx;
}

.img-logisc {
  width: 160rpx;
  height: 40rpx;
  float: left;
  display: inline;
  position: relative;
  top: 4rpx;
  left: 0;
  margin-right: 8rpx;
}

.tag-duty {
  line-height: 48rpx;
  background: #e5f4ff;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #0092ff;
  padding: 0 12rpx;
  margin-right: 8rpx;
  flex: 0 0 auto;
}

.footer-ext-container {
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;
  margin-left: auto;
}

.footer-ext-right {
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  align-items: center;
}

.dot-green {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
  background: rgba(6, 181, 120, 1);
}

.title-box {
  display: flex;
  justify-content: space-between;
}

.salary,
.salary-new {
  color: #0092ffff;
  font-weight: 500;
  font-size: 30rpx;

  .salary-text {
    margin-bottom: 16rpx;
    line-height: 1;
  }
  .is-new-zp-card {
    margin-bottom: 0;
    line-height: 48rpx;
  }
}

.salary .salary-text {
  margin-bottom: 0;
  margin-top: 16rpx;
}

.salary-new {
  flex-shrink: 0;
  margin-left: 16rpx;
}

.info {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .address-box {
    display: flex;
  }

  .address-text,
  .address-count,
  .time {
    color: #00000073;
    font-weight: 400;
    font-size: 24rpx;
    line-height: 34rpx;
  }

  .address-text {
    max-width: 300rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
  }

  .address-count {
    margin-left: 8rpx;
  }

  .time {
    color: #00000040;
  }
}

.user-footer {
  padding: 16rpx 0rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .user-avatar-box {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .user-box {
    display: flex;
    align-items: center;
  }

  .user-online {
    position: absolute;
    bottom: -3rpx;
    right: 12rpx;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    border: 2rpx solid #ffffffff;
    background: rgba(6, 181, 120, 1);
    box-sizing: border-box;
  }


  .user-avatar {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }

  .user-info-box {
    display: flex;
    flex-direction: column;
    gap: 4rpx
  }

  .user-name,
  .user-desc {
    color: #000000d9;
    font-weight: 400;
    font-size: 24rpx;
    line-height: 34rpx;

    max-width: 360rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .user-desc {
    color: #00000073;
  }
}

.recommend-box {
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  line-height: 34rpx;
  font-size: 24rpx;
  background-color: #F5F6FAFF;
  padding: 12rpx 16rpx;
  color: #00000073;
  display: flex;
  align-items: center;

  .icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
    transform: translateY(-2rpx);
  }
}

.fallback {
  background-color: #F5F7FCFF;
}

