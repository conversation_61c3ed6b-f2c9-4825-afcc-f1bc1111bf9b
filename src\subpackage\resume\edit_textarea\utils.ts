import { store } from '@/store/index'

export const infoData = {
  /** 个人优势 */
  introduce: {
    title: '我的优势',
    desc: '丰富的个人优势介绍，更能赢得老板青睐',
    placeholder: '等待输入内容',
  },
  /** 专业技能 */
  majorSkill: {
    title: '技能证书',
    desc: '根据熟练程度，可用精通、熟练掌握、了解开头',
    maxContent: 1000,
    placeholder: '等待输入内容',
  },
  /** 工作内容 */
  workCont: {
    title: '工作内容',
    desc: '描述工作背景，负责的工作内容、克服过的困难、取得的工作成果，便于老板了解你丰富的经验',
    maxContent: 1600,
    placeholder: '1、主要负责新员工入职培训\n'
      + '2、分析制定员工每月个人销售业绩\n'
      + '3、帮助员工提高每日客单价，管理店面等工作',
  },
  /** 工作业绩 */
  workKPI: {
    title: '工作业绩',
    desc: '',
    placeholder: '展现完整、有吸引力的业绩，吸引更多的老板关注\n'
      + '例如:\n\n'
      + '1、取得的成绩\n'
      + '2、实现的突破\n'
      + '3、获得的表彰',
  },
  /** 项目描述 */
  project: {
    title: '项目描述',
    desc: '简述项目内容，向老板展示你丰富的经验',
    maxContent: 1600,
    placeholder:
      '例如:\n'
      + '1、项目概况\n'
      + '2、人员分工\n'
      + '3、我的责任',
  },
  /** 教育在校经历 */
  eduExperience: {
    title: '在校经历',
    desc: '',
    placeholder: '1、在校担任职务\n'
      + '2、获得荣誉\n'
      + '3、所学主要课程',
  },
}

export type IType = keyof typeof infoData

/** 获取初始输入框的值 */
export function getContent(type: IType) {
  const details = wx.$.u.deepClone(store.getState().storage.myResumeDetails)
  let content = wx.$.nav.getData().content || ''

  if (type === 'introduce') {
    content = wx.$.u.getObjVal(details, 'basicResp.introduce')
  } else if (type === 'majorSkill') {
    content = wx.$.u.getObjVal(details, 'professionalSkills.skills')
  }

  return content
}

/** 个人优势编辑(自我介绍) */
export async function editIntroduce(introduce: string) {
  const { resumeUuid } = store.getState().storage.myResumeDetails
  wx.$.loading('保存中...')
  const res = await wx.$.javafetch['POST/resume/v3/perfect/introduce']({
    resumeUuid,
    introduce,
  }).catch((err) => err)
  wx.hideLoading()
  const { code, popup } = res || {}
  if (code != 0 && popup) {
    wx.$.showModal({
      ...popup,
    })
  } else if (res.code == 0) {
    wx.$.msg('保存成功', 2000, true).then(() => {
      wx.$.r.back()
    })
  } else {
    wx.$.msg(res.msg || '保存失败')
  }
  return res
}

/** 专业技能编辑 */
export async function editMajorSkill(skills: string) {
  const { resumeUuid } = store.getState().storage.myResumeDetails
  wx.$.loading('保存中...')
  const res = await wx.$.javafetch['POST/resume/v3/perfect/skills']({
    resumeUuid,
    skills,
  }).catch((err) => err)
  wx.hideLoading()
  const { code, popup } = res || {}
  if (code != 0 && popup) {
    wx.$.showModal({
      ...popup,
    })
  } else if (res.code == 0) {
    wx.$.msg('保存成功', 2000, true).then(() => {
      wx.$.r.back()
    })
  } else {
    wx.$.msg(res.msg || '保存失败')
  }
  return res
}
