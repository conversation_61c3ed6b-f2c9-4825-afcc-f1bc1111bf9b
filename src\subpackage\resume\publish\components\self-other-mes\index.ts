/**
 * 自定义添加社团组织经历组件
 * 用于在简历发布页面显示可添加的各类经历项目
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 专业技能数据
    professionalSkills: {
      type: Object,
      value: {},
    },
    // 简历荣誉奖励数据
    resumeAwards: {
      type: Array,
      value: [],
    },
    // 简历实践活动数据
    resumePractices: {
      type: Array,
      value: [],
    },
    // 简历组织经历数据
    resumeOrganizationExperiences: {
      type: Array,
      value: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 是否显示整个组件框
    shouldShowBox: false,
    // 是否已有专业技能
    hasProfessionalSkills: false,
    // 是否已有组织经历
    hasOrganizationExperiences: false,
    // 是否已有实践活动
    hasPractices: false,
    // 是否已有荣誉奖励
    hasAwards: false,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理添加技能/经历/活动/奖励的点击事件
     * @param {Object} e 事件对象
     */
    handAddSkill(e) {
      const { type } = e.currentTarget.dataset

      // 定义类型与页面路径的映射关系
      const pageMap = {
        major: '/subpackage/resume/edit_textarea/index?type=majorSkill', // 专业技能编辑页
        reward: '/subpackage/resume/publish/pages/honor-award-edit/index', // 荣誉奖励编辑页
        activity: '/subpackage/resume/publish/pages/practice-activity-edit/index', // 实践活动编辑页
        group: '/subpackage/resume/publish/pages/organization-experience-edit/index', // 组织经历编辑页
      }
      const url = pageMap[type]
      if (url) {
        wx.navigateTo({ url })
      }
    },

    /**
     * 计算并更新显示状态
     * 当属性变化时调用此方法
     */
    updateDisplayStatus() {
      // 计算专业技能是否已存在
      const hasProfessionalSkills = this.data.professionalSkills?.skills?.length > 0

      // 计算组织经历是否已存在
      const hasOrganizationExperiences = this.data.resumeOrganizationExperiences?.length > 0

      // 计算实践活动是否已存在
      const hasPractices = this.data.resumePractices?.length > 0

      // 计算荣誉奖励是否已存在
      const hasAwards = this.data.resumeAwards?.length > 0

      // 计算是否应该显示整个组件框
      const shouldShowBox = !hasProfessionalSkills
                                  || !hasOrganizationExperiences
                                  || !hasPractices
                                  || !hasAwards

      // 更新数据
      this.setData({
        shouldShowBox,
        hasProfessionalSkills,
        hasOrganizationExperiences,
        hasPractices,
        hasAwards,
      })
    },
  },

  /**
   * 组件生命周期函数-在组件实例进入页面节点树时执行
   */
  attached() {
    // 组件初始化时计算显示状态
    this.updateDisplayStatus()
  },
  /**
   * 监听器 - 当属性变化时自动调用
   */
  observers: {
    // 监听所有属性变化，自动更新显示状态
    'professionalSkills, resumeAwards, resumePractices, resumeOrganizationExperiences': function () {
      this.updateDisplayStatus()
    },
  },
})
