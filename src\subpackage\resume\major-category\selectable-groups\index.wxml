<scroll-view
  class="selectable-groups"
  scroll-y="true"
  scroll-into-view="{{ scrollIntoView }}"
  scroll-top="{{ scrollTop }}"
  style="height: calc(100vh - {{ top }}px)"
  scroll-with-animation="{{ enableScrollAnimation }}"
  bindscroll="onScroll"
>
  <block wx:for="{{ groups }}" wx:key="index">
    <view class="group" id="{{ item.key }}">
      <view class="group-label">
        <view>
          <text class="title">{{item.label}}</text>
        </view>
      </view>
      <view class="options">
        <block wx:for="{{ item.options }}" wx:key="value" wx:for-item="option" wx:for-index="idx">
          <view
            class="option-item {{ option.selected ? 'selected' : ''}}"
            data-group="{{ item.key }}"
            data-group-label="{{ option.label }}"
            data-value="{{ option.value }}"
            bindtap="onSelectItem"
          >
            {{option.label}}
          </view>
        </block>
      </view>
    </view>
  </block>
</scroll-view>
