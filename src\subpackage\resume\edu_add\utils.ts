/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: wyl <EMAIL>
 */

import { getEduData, showEduStr } from '@/subpackage/resume/components/my-detail/edu-picker/utils'

/** 处理提交时间的逻辑 */
export function handlerSubTime(time) {
  if (!time || time == '1990年以前' || time == '至今') {
    return null
  }
  const year = `${time}`.split('.')[0] // 取出小数点前的数（年份）
  let month = `${time}`.split('.')[1] || '01' // 取出小数点后的数（月份）
  // 确保月份是两位数
  if (month && month.length < 2) {
    month = `0${month}`
  }

  // 组合成所需的格式
  return `${year}-${month}`
}

/** 处理提交的数据 */
export function getParams(formData) {
  const { eduStr,
    checkErrMsg,
    checkStatus,
    timeData,
    ...other } = formData
  return other
}

function stringNull(str) {
  if (!str) {
    return true
  }
  return `${str}`.trim() == ''
}

/** 判断是否可以提交，如果可以提交返回提交的数据 */
export function isSubmit(formData): any | false {
  const { isEduMajor } = getEduData(formData.eduBackground)
  if (stringNull(formData.schoolName)) {
    wx.$.msg('请填写学校')
    return false
  }
  if (stringNull(formData.eduBackground)) {
    wx.$.msg('请选择学历')
    return false
  }
  if (isEduMajor && stringNull(formData.majorName)) {
    wx.$.msg('请填写专业')
    return false
  }
  if (isEduMajor && stringNull(formData.majorCategoryId)) {
    wx.$.msg('请选择专业类别')
    return false
  }
  if (!formData.startTime || !formData.endTime) {
    wx.$.msg('请选择入学时间')
    return false
  }
  return getParams(formData)
}

export function getInitFormData(dataInfo: any) {
  const { startTime,
    endTime,
    ...other } = dataInfo

  const nStartTime = startTime == null ? '1990年以前' : startTime || ''
  const nEndTime = endTime == null ? '至今' : endTime || ''
  const eduDataInfo = getEduData(dataInfo.eduBackground)

  return {
    isEduMajor: eduDataInfo.isEduMajor || false,
    formData: {
      ...other,
      uuid: dataInfo.uuid,
      startTime: nStartTime,
      endTime: nEndTime,
      eduStr: showEduStr(dataInfo.eduBackground, dataInfo.eduType),
      timeData: `${nStartTime}-${nEndTime}`,
      // TODO: lx确保专业类别字段存在
      majorCategoryId: dataInfo.majorCategoryId || '',
      majorCategoryName: dataInfo.majorCategoryName || '',
    },
  }
}
