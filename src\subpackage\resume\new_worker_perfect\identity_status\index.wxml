<view class="worker-perfect">
  <worker-perfect-title title="你的身份" prevPath="{{prevPath}}" />

  <!-- 身份选择 -->
  <view class="cell">
    <view class="identity-options">
      <view class="identity-item {{identityInfo.identity == 1 ? 'selected' : ''}}" data-type="identity" data-value="{{1}}" bind:tap="onIdentityChange">
        职场人
      </view>
      <view class="identity-item {{identityInfo.identity == 2 ? 'selected' : ''}}" data-type="identity" data-value="{{2}}" bind:tap="onIdentityChange">
        学生
      </view>
    </view>
    <!-- 身份提示文案 -->
    <view class="identity-tip" wx:if="{{identityInfo.identity}}">
      <text wx:if="{{identityInfo.identity == 1}}">职场人指有工作经验的人群</text>
      <text wx:if="{{identityInfo.identity == 2}}">学生包含在校生、应届生、往届生</text>
    </view>
  </view>

  <!-- 求职状态选择 -->
  <view class="cell" wx:if="{{identityInfo.identity}}">
    <view class="cell-title">求职状态</view>
    <view class="job-status-grid">
      <!-- 职场人选项 -->
      <block wx:if="{{identityInfo.identity == 1}}">
        <view class="job-status-item {{identityInfo.jobStatus == 1 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{1}}" bind:tap="onJobStatusChange">
          <view class="recommend-tag" wx:if="{{identityInfo.jobStatus == 1 || (!identityInfo.jobStatus)}}">
            <icon-font type="yp-dianzan" size="24rpx" color="#0092FF" />
            <text>推荐</text>
          </view>
          <text class="status-text">离职-随时到岗</text>
        </view>
        <view class="job-status-item {{identityInfo.jobStatus == 2 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{2}}" bind:tap="onJobStatusChange">
          <view class="recommend-tag" wx:if="{{identityInfo.jobStatus == 2 || (!identityInfo.jobStatus)}}">
            <icon-font type="yp-dianzan" size="24rpx" color="#0092FF" />
            <text>推荐</text>
          </view>
          <text class="status-text">在职-月内到岗</text>
        </view>
        <view class="job-status-item {{identityInfo.jobStatus == 3 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{3}}" bind:tap="onJobStatusChange">
          <text class="status-text">在职-考虑机会</text>
        </view>
        <view class="job-status-item {{identityInfo.jobStatus == 4 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{4}}" bind:tap="onJobStatusChange">
          <text class="status-text">在职-暂不考虑</text>
        </view>
      </block>

      <!-- 学生选项 -->
      <block wx:if="{{identityInfo.identity == 2}}">
        <view class="job-status-item {{identityInfo.jobStatus == 1 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{1}}" bind:tap="onJobStatusChange">
          <view class="recommend-tag" wx:if="{{identityInfo.jobStatus == 1 || (!identityInfo.jobStatus)}}">
            <icon-font type="yp-dianzan" size="24rpx" color="#0092FF" />
            <text>推荐</text>
          </view>
          <text class="status-text">离校-随时到岗</text>
        </view>
        <view class="job-status-item {{identityInfo.jobStatus == 2 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{2}}" bind:tap="onJobStatusChange">
          <view class="recommend-tag" wx:if="{{identityInfo.jobStatus == 2 || (!identityInfo.jobStatus)}}">
            <icon-font type="yp-dianzan" size="24rpx" color="#0092FF" />
            <text>推荐</text>
          </view>
          <text class="status-text">在校-月内到岗</text>
        </view>
        <view class="job-status-item {{identityInfo.jobStatus == 3 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{3}}" bind:tap="onJobStatusChange">
          <text class="status-text">在校-考虑机会</text>
        </view>
        <view class="job-status-item {{identityInfo.jobStatus == 4 ? 'selected' : ''}}" data-type="jobStatus" data-value="{{4}}" bind:tap="onJobStatusChange">
          <text class="status-text">在校-暂不考虑</text>
        </view>
      </block>
    </view>

    <!-- 求职状态提示文案 -->
    <view class="job-status-tip">
      求职状态会影响到你被推荐的频率,以及老板的决策
    </view>
  </view>

  <worker-perfect-footer
    pageTitle="身份选择"
    isSkip="{{processConfig.jumpSwitch}}"
    disabled="{{!identityInfo.identity || !identityInfo.jobStatus}}"
    jumpPath="{{jumpPath}}"
    processConfig="{{processConfig}}"
    bottomHeight="{{bottomHeight}}"
    bind:onSubmit="onSubmit"
    btnText="{{btnText}}"
  />
</view>
