<view class="worker-perfect">
  <worker-perfect-title title="你的身份" prevPath="{{prevPath}}" />

  <!-- 身份选择 -->
  <view class="cell">
    <view class="identity-options">
      <view class="identity-item {{identityInfo.identity == 1 ? 'selected' : ''}}" data-type="identity" data-value="{{1}}" bind:tap="onIdentityChange">
        职场人
      </view>
      <view class="identity-item {{identityInfo.identity == 2 ? 'selected' : ''}}" data-type="identity" data-value="{{2}}" bind:tap="onIdentityChange">
        学生
      </view>
    </view>
    <!-- 身份提示文案 -->
    <view class="identity-tip" wx:if="{{identityTip}}">
      {{identityTip}}
    </view>
  </view>

  <!-- 求职状态选择 -->
  <view class="cell" wx:if="{{identityInfo.identity}}">
    <view class="cell-title">求职状态</view>
    <view class="job-status-grid">
      <view
        wx:for="{{jobStatusOptions}}"
        wx:key="value"
        class="job-status-item {{identityInfo.jobStatus == item.value ? 'selected' : ''}}"
        data-type="jobStatus"
        data-value="{{item.value}}"
        bind:tap="onJobStatusChange">
        <view class="recommend-tag {{item.isRecommend ? 'tag-selected' : ''}}" wx:if="{{item.isRecommend}}">
          <image src="https://cdn.yupaowang.com/yupao_common/fb3ad230.png" class="img" mode="aspectFill" />
          <text>推荐</text>
        </view>
        <text class="status-text">{{item.label}}</text>
      </view>
    </view>

    <!-- 求职状态提示文案 -->
    <view class="job-status-tip">
      求职状态会影响到你被推荐的频率,以及老板的决策
    </view>
  </view>

  <worker-perfect-footer
    pageTitle="身份选择"
    isSkip="{{processConfig.jumpSwitch}}"
    disabled="{{!identityInfo.identity || !identityInfo.jobStatus}}"
    jumpPath="{{jumpPath}}"
    processConfig="{{processConfig}}"
    bottomHeight="{{bottomHeight}}"
    bind:onSubmit="onSubmit"
    btnText="{{btnText}}"
  />
</view>
