<view id="custom-header">
  <custom-header title="我的在线简历" />
</view>
<view class="body">
  <view class="container">
    <!-- 隐藏简历的信息 -->
    <block wx:if="{{hideStatus == 1}}">
      <hide-card />
    </block>
    <!-- 完善分 -->
    <block wx:if="{{diyDetails.progressScore < 100}}">
      <perfect-score score="{{diyDetails.progressScore}}" />
    </block>
    <!-- 用户信息 -->
    <user-info dataSource="{{diyDetails.userInfo}}" />
    <!-- 求职状态 -->
    <job-status dataSource="{{diyDetails.jobStatus}}" bind:change="onRefreshChange" data-type="refreshOne" />
    <!-- 个人优势 -->
    <block wx:if="{{showElem.isIntroduce || diyDetails.basicInfo.introduce}}">
      <introduce dataSource="{{diyDetails.basicInfo}}" />
    </block>
    <!-- 工作城市 -->
    <block wx:if="{{showElem.isCity || diyDetails.basicInfo.hopeAreaCity.length}}">
      <job-city dataSource="{{diyDetails.basicInfo}}" bind:change="onRefreshChange" data-type="refreshOne" />
    </block>
    <!-- 求职期望 -->
    <resume-job dataSource="{{diyDetails.subJob}}" />
    <!-- 工作经历 -->
    <block wx:if="{{showElem.isWork || diySecondDetails.workExpResp.length}}">
      <work-experience dataSource="{{diySecondDetails.workExpResp}}" />
    </block>
    <!-- 项目经历 -->
    <block wx:if="{{showElem.isProject || diySecondDetails.projectExpResp.length}}">
      <project-experience dataSource="{{diySecondDetails.projectExpResp}}" />
    </block>
    <!-- 教育经历 -->
    <block wx:if="{{showElem.isEdu || diySecondDetails.eduExpResp.length}}">
      <education dataSource="{{diySecondDetails.eduExpResp}}" />
    </block>
    <!-- 资格证书 -->
    <certificate wx:if="{{showElem.isCertificate || diySecondDetails.userCertificateInfos.length > 0}}" dataSource="{{diySecondDetails.userCertificateInfos}}" resumeUuid="{{diyDetails.basicInfo.resumeUuid}}"/>
    <!-- 专业技能 -->
    <block wx:if="{{showElem.isProfessionalSkills || diySecondDetails.professionalSkills.skills}}">
      <professional-skills dataSource="{{diySecondDetails.professionalSkills}}" />
    </block>

    <!-- 视频简历 -->
    <!-- #ifdef weapp -->
    <block wx:if="{{showElem.isVideo || diyDetails.videoResp.videoUrl}}">
      <interview-video id="interviewVideo" dataSource="{{diyDetails.videoResp}}" />
    </block>
    <!-- #endif -->


    <!-- 找工作法宝 -->
    <resume-skills
      dataSource="{{diyDetails.resumeSkillsInfo}}"
      bind:pay="onPayPop"
      data-type="refreshOne"
      bind:refresh="onRefreshChange"
      bind:showRefresh="onShowRefresh"
    />

    <!-- 自定义添加 -->
    <self-other-mes
      professionalSkills="{{diyDetails.resumeSkillsInfo}}"
      resumeAwards="{{diyDetails.resumeAwards}}"
      resumePractices="{{diyDetails.resumePractices}}"
      resumeOrganizationExperiences="{{diyDetails.resumeOrganizationExperiences}}"
    />
  </view>
</view>

<!-- 附件简历-悬浮按钮 -->
<attachment
  wx:if="{{showElem.isAttachment}}"
  score="{{diyDetails.progressScore}}"
  isAttachAnime="{{isAttachAnime}}"
  bind:showRefresh="onShowRefresh"
/>

<!-- 积分充值弹窗 -->
<block wx:if="{{showPop.type == 'rechargePopup'}}">
  <recharge-popup show="{{true}}" bind:success="onRechargeSuccess" bind:close="onClosePop">
    <view wx:if="{{showPop.data.length > 0}}" slot="header" class="recharge-header">
      <block wx:for="{{showPop.data}}" wx:key="index">
        <view style="color:{{item.color}};">{{item.text}}</view>
      </block>
    </view>
  </recharge-popup>
</block>

<!-- 简历引导完善-v4.0.0-引导完善个人信息、职位偏好、教育经历、项目经历、工作经历等 -->
<pop-setinfo isInitLoad="{{isInitLoad}}" bind:showRefresh="onShowRefresh" />
