/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: xia<PERSON><PERSON><PERSON>un <EMAIL>
 * @Description: 处理数据找活名片信息
 * @param info
 */

import { MyResumeDetails } from '@/store/model/storage/defData'

import formatSubJob from '@/subpackage/resume/utils/resumeFormat/formatSubJob'
import formatUserInfo from '@/subpackage/resume/utils/resumeFormat/formatUserInfo'
import { dialogTemplateHtml } from '@/utils/helper/dialog/index'
import { getFmtTemplateText, resumeNotExistsPop } from '@/subpackage/resume/utils/index'
import { getTempShow } from '@/utils/helper/resume/utils'
import { isIos } from '@/utils/tools/validator/index'
import { store } from '@/store/index'
import * as dialog from './dialog'
import { getWorkStatusName } from './dictKeys'

export const { refreshDialog } = dialog

/** 处理用户找活名片详情数据
 * @param resumeDetail
 */
export const formatResumeDetails = async (detail: MyResumeDetails) => {
  const resumeDetail = wx.$.u.deepClone(detail)
  // 主名片审核状态 1:待审核,2:审核通过,3:审核不通过
  const { basicResp: basicInfo,
    progressScore } = resumeDetail

  const subJob = formatSubJob(resumeDetail.basicResp.subs || [])
  console.log('subJob:', subJob)

  return {
    /** 名片完善度 */
    progressScore,
    /** 用户信息 */
    userInfo: formatUserInfo(resumeDetail),
    /** 找活的uuid */
    resumeUuid: basicInfo.resumeUuid,
    /** 找活基础信息 */
    basicInfo,
    /** 求职状态 */
    jobStatus: {
      /** 求职状态 */
      workStatus: basicInfo.workStatus,
      /** 求职状态名称 */
      workStatusName: getWorkStatusName(basicInfo.workStatus),
    },
    /** 求职期望-子名片列表 */
    subJob,
    /** 视频简历 */
    videoResp: resumeDetail.videoResp || {},
    /** 找工作法宝 */
    resumeSkillsInfo: {
      /** 主名片审核状态 1:待审核,2:审核通过,3:审核不通过 */
      checkStatus: basicInfo.checkStatus,
      /** 加急服务信息 */
      rightTopResp: resumeDetail.rightTopResp || {},
      /** 刷新服务信息 */
      rightRefreshResp: resumeDetail.rightRefreshResp || {},
    },
  }
}

/** 处理显示规则
 * 个人优势 F_SelfIntroduction
 * 工作城市 F_City
 * 工作经历 F_WorkExperience
 * 项目经历 F_ProjectExperience
 * 教育经历 F_EducationalExperience
 * 资格证书（延后不搞） F_Credentials
 * 视频简历 F_VideoResume
 * 附件简历 F_UploadResumeAttachment
 */
export async function handlerShowDetail() {
  // 获取简历名片信息
  const { myResumeDetails } = store.getState().storage
  const res = await getTempShow(myResumeDetails.occIds)

  const showElem = {
    isIntroduce: false, // 个人优势
    isCity: false, // 工作城市
    isWork: false, // 工作经历
    isProject: false, // 项目经历
    isEdu: false, // 教育经历
    isCertificate: false, // 资格证书
    isVideo: false, // 视频简历
    isAttachment: false, // 附件简历
    isProfessionalSkills: false, // 专业技能
  }

  const controlCodeMap = {
    F_SelfIntroduction: 'isIntroduce',
    F_City: 'isCity',
    F_WorkExperience: 'isWork',
    F_ProjectExperience: 'isProject',
    F_EducationalExperience: 'isEdu',
    F_Credentials: 'isCertificate',
    F_VideoResume: 'isVideo',
    F_UploadResumeAttachment: 'isAttachment',
    F_ProfessionalSkills: 'isProfessionalSkills',
  }

  res.templates.forEach(({ templateInfo }) => {
    const infoList: any[] = wx.$.u.getObjVal(templateInfo, 'controlInfoList') || []

    infoList.forEach(({ controlCode, status }) => {
      const key = controlCodeMap[controlCode]
      if (status == 1 && key && !showElem[key]) {
        showElem[key] = true
      }
    })
  })
  return showElem
}

/** 名片刷新
 * @param $params
 * @param query 跳转的路由参数
 */
export async function fetchResumeRefresh($params?: YModels['POST/resume/v3/refresh/app/refresh']['Req'], query = {}): Promise<any> {
  const params = $params || { popupList: [] }

  wx.$.loading('刷新中...')
  return new Promise(resolve => {
    // wx.$.javafetch['POST/resume/v1/resumeRefresh/refresh']({ source: 1 }, {

    wx.$.javafetch['POST/resume/v3/refresh/app/refresh'](params, {
      hideMsg: true,
      showErrCodes: ['10001'],
    }).then((res) => {
      wx.hideLoading()
      const textToast = res.error ? '简历刷新失败' : '简历刷新成功'
      const resCode = res.code
      switch (Number(resCode)) {
        case 28010001: // 找活名片不存在
          resumeNotExistsPop()
          return resolve('')
        case 28010005: // 您的简历正在审核中，通过后才可操作！
        case 28111001: // 请勿频繁刷新
          wx.$.msg(res.message || textToast)
          return resolve('')
        case 30011:
          wx.$.r.push({
            path: '/subpackage/resume/continuous_refresh/index',
            query: {
              type: 'refreshSuccessful',
              ...query,
            },
          })
          return resolve('')
        case 651: { // 积分不足的弹框
          const template: any = res.data || {}
          if (!isIos()) {
            /** 积分不足-不是ios环境-弹出充值积分弹框 */
            return resolve({
              ...res,
              isPay: true,
              popData: {
                type: 'rechargePopup',
                data: getFmtTemplateText(template),
              },
            })
          }
          /** 如果是ios环境 */
          const richContent = dialogTemplateHtml(template)
          wx.$.confirm({
            title: '温馨提示',
            richContent,
            confirmText: '获取积分',
            cancelText: '取消',
          }).then(() => {
            wx.$.toGetIntegral()
          }).catch(() => { })

          return resolve('')
        }
        default:
      }

      if (!res.data) {
        wx.$.msg(res.message || textToast)
        return resolve({ error: res.code != 0 })
      }

      const { dialogData } = res.data as any

      if (res.data.dialogIdentify || dialogData) {
        refreshDialog(res)
        let dialogIdentify = res.data ? res.data.dialogIdentify : ''
        if (!dialogIdentify && dialogData) {
          dialogIdentify = dialogData.dialogIdentify
        }
        const error = !(dialogIdentify === 'shuaxinjiajiyindaojiaji' || res.code == 0)
        return resolve({ error })
      }
      wx.$.msg(res.message || textToast)
      return resolve(true)
    }).catch((err) => {
      wx.hideLoading()
      if (err && err.code == 10001) {
        return resolve('')
      }
      wx.$.msg(err.message || '简历刷新失败')
      return resolve('')
    })
  })
}

/** 进入我的找活名片页-埋点 */
export const resumePageBasis = function (ext = {}) {
  const { myResumeDetails } = store.getState().storage
  // 埋点
  wx.$.collectEvent.event('myBusinessCard', {
    /** 名片完善度 */
    business_card_perfection: `${myResumeDetails.progressScore}%`,
    /** 推荐人数 */
    // recommended_number: myResumeDetails.basicInfoResp.exposureMe,
    /** 浏览人数 */
    // visitors_number: myResumeDetails.basicInfoResp.seeMe,
    /** 子名片数量 */
    number_business_cards: myResumeDetails.subCount,
    /** 工作状态 */
    active_status: myResumeDetails.basicResp.workStatus,
    ...ext,
  })
}

/** 找活法宝监听元素，并埋点 resumeSkills */
export function observerSkillsPoint(targetSelector: string, info_id: string) {
  let isPoint = false // 是否已经埋点
  const observer = wx.createIntersectionObserver(this, {
    thresholds: [1, 100],
    initialRatio: 0,
    observeAll: true,
  })
  observer.relativeToViewport({ top: 80, bottom: 0 })
  observer.observe(targetSelector, () => {
    if (!isPoint) {
      // clickPointExp({ operation_type, page_name })
      wx.$.collectEvent.event('magicWeaponExposure', {
        source: '我的找活名片_底部',
        info_id,
      })
      isPoint = true
      observer.disconnect() // 卸载
    } else {
      observer.disconnect() // 卸载
    }
  })
}
